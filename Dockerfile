# Многоэтапная сборка для оптимизации размера образа
FROM python:3.11-slim as builder

# Устанавливаем системные зависимости для сборки
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Создаем виртуальное окружение
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Копируем и устанавливаем зависимости
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Продакшн образ
FROM python:3.11-slim

# Устанавливаем только runtime зависимости
RUN apt-get update && apt-get install -y \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Копируем виртуальное окружение из builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Создаем непривилегированного пользователя
RUN groupadd -r app && useradd -r -g app app

# Создаем рабочую директорию
WORKDIR /app

# Копируем код приложения
COPY --chown=app:app . .

# Сохраняем статические файлы в отдельное место до монтирования volume
RUN if [ -d "/app/app/static" ]; then \
        cp -r /app/app/static /tmp/app_static_backup; \
    fi

# Устанавливаем права на выполнение для скрипта запуска
RUN chmod +x /app/scripts/docker-entrypoint.sh

# Создаем директории для логов и данных
RUN mkdir -p /app/logs /app/data \
    && chown -R app:app /app

# Переключаемся на непривилегированного пользователя
USER app

# Экспонируем порт
EXPOSE 8000

# Проверка здоровья контейнера
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Команда по умолчанию для продакшн
CMD ["/app/scripts/docker-entrypoint.sh"]
