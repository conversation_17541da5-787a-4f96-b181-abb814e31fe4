[settings]
# Профиль для FastAPI проектов
profile = black
multi_line_output = 3
include_trailing_comma = True
force_grid_wrap = 0
use_parentheses = True
ensure_newline_before_comments = True
line_length = 88

# Секции импортов
sections = FUTURE,STDLIB,THIRDPARTY,FIRSTPARTY,LOCALFOLDER
default_section = THIRDPARTY

# Известные библиотеки первой стороны (наш проект)
known_first_party = app

# Известные сторонние библиотеки
known_third_party = fastapi,uvicorn,sqlalchemy,alembic,asyncpg,psycopg2,pydantic,httpx,aiohttp,pytest,structlog,jinja2,passlib,jose

# Пропускать файлы
skip = __pycache__,migrations,.git,.mypy_cache,venv,env,.venv,.env,alembic/versions

# Принудительно сортировать в алфавитном порядке
force_sort_within_sections = True

# Добавлять пустую строку после импортов
add_imports = from __future__ import annotations
