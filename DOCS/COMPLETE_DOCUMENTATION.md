# 📚 Полная документация SMS Proxy Service

## 📋 Оглавление

1. [Обзор проекта](#обзор-проекта)
2. [Архитектура системы](#архитектура-системы)
3. [Защита от Race Conditions](#защита-от-race-conditions)
4. [Руководство по развертыванию](#руководство-по-развертыванию)
5. [API документация](#api-документация)
6. [База данных](#база-данных)
7. [Безопасность и мониторинг](#безопасность-и-мониторинг)
8. [Устранение неполадок](#устранение-неполадок)

---

## 🎯 Обзор проекта

**SMS Proxy Service** — это FastAPI-сервис-прокси между Firefox и SMSLive, полностью совместимый с форматом SMSActivate API. Система предоставляет единый интерфейс для работы с несколькими провайдерами виртуальных номеров.

### 🚀 Основные возможности

- **Совместимость с SMSActivate API** - Полная совместимость с существующими клиентами
- **Мультипровайдерность** - Поддержка нескольких SMS провайдеров (Firefox, SMSLive)
- **Автоматическое переключение** - Умное переключение между провайдерами при недоступности
- **Административная панель** - Веб-интерфейс для управления системой
- **Мониторинг и логирование** - Подробное логирование всех операций
- **Защита от высокой нагрузки** - Комплексная защита от race conditions и DDoS

### 🛠️ Технологический стек

- **Backend**: FastAPI + Python 3.9+
- **Database**: PostgreSQL с асинхронной поддержкой
- **ORM**: SQLAlchemy 2.0 с Alembic для миграций
- **Deployment**: Docker + Docker Compose
- **Web Server**: Nginx (reverse proxy)
- **Monitoring**: Встроенное логирование и мониторинг

---

## 🏗️ Архитектура системы

### Компоненты системы

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Клиенты       │    │   FastAPI Proxy  │    │   Провайдеры    │
│                 │◄──►│     Service      │◄──►│                 │
│ - Firefox       │    │                  │    │ - Firefox SMS   │
│ - Пользователи  │    │ - Маршрутизация  │    │ - SMSLive       │
│ - API клиенты   │    │ - Бизнес-логика  │    │                 │
└─────────────────┘    │ - Валидация      │    └─────────────────┘
                       │ - Логирование    │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   PostgreSQL     │
                       │                  │
                       │ - Пользователи   │
                       │ - Активации      │
                       │ - Транзакции     │
                       │ - Логи           │
                       └──────────────────┘
```

### Основные сервисы

#### 1. **Activation Service** (`app/services/activation_service.py`)
- Управление жизненным циклом активаций
- Взаимодействие с провайдерами
- Обработка таймаутов (10 минут)

#### 2. **Balance Service** (`app/services/balance_service.py`)
- Управление балансом пользователей
- Пессимистические блокировки для защиты от race conditions
- Транзакционная безопасность

#### 3. **User Service** (`app/services/user_service.py`)
- Управление пользователями
- Аутентификация по API ключам
- Административные функции

#### 4. **Price Service** (`app/services/price_service.py`)
- Управление ценами на услуги
- Интеграция с провайдерами
- Кэширование цен

---

## 🛡️ Защита от Race Conditions и проблем при высокой нагрузке

### Проблемы, решаемые системой

При высокой нагрузке система может столкнуться со следующими проблемами:

#### 1. **Race Conditions**
- Два запроса одновременно читают баланс 100₽
- Оба думают что средств достаточно
- Оба резервируют по 60₽, хотя должен был пройти только один

#### 2. **Dirty Read**
- Один запрос читает баланс пока другой его обновляет
- Получение некорректных данных

#### 3. **Lost Updates**
- Обновления перезаписывают друг друга
- Потеря данных о транзакциях

### Реализованные защиты

#### ✅ 1. Пессимистические блокировки (SELECT FOR UPDATE)

```python
async def _get_user_with_lock(self, user_id: int, for_update: bool = False) -> User:
    query = select(User).where(User.id == user_id)

    if for_update:
        # Используем пессимистическую блокировку
        query = query.with_for_update()

    result = await self.db.execute(query)
    user = result.scalar_one_or_none()
```

**Принцип работы:**
- Блокирует строку пользователя на время транзакции
- Другие запросы ждут освобождения блокировки
- Гарантирует атомарность операций

#### ✅ 2. Проверка баланса на уровне БД

```python
# Атомарное обновление баланса на уровне БД
result = await self.db.execute(
    update(User)
    .where(and_(
        User.id == user_id,
        User.balance >= amount  # Проверка на уровне БД
    ))
    .values(balance=User.balance - amount)
)

# Проверяем, что обновление прошло успешно
if result.rowcount == 0:
    raise InsufficientFundsError("Не удалось списать средства")
```

#### ✅ 3. Ограничения целостности на уровне БД

```python
__table_args__ = (
    CheckConstraint('balance >= 0', name='check_balance_non_negative'),
)
```

#### ✅ 4. Rate Limiting

- **Пользовательский**: 10 запросов в минуту на пользователя
- **IP-адрес**: 100 запросов в минуту на IP
- Защита от спама и DDoS атак

#### ✅ 5. Retry механизм с экспоненциальной задержкой

```python
async def _retry_operation(self, operation, *args, **kwargs):
    for attempt in range(self._max_retries):
        try:
            return await operation(*args, **kwargs)
        except (OperationalError, IntegrityError) as e:
            if attempt == self._max_retries - 1:
                break

            # Экспоненциальная задержка: 100мс, 200мс, 400мс
            delay = self._base_delay * (2 ** attempt)
            await asyncio.sleep(delay)
```

#### ✅ 6. Валидация на уровне модели

```python
@validates('balance')
def validate_balance(self, key, value):
    if value < 0:
        raise ValueError("Баланс не может быть отрицательным")
    return value
```

---

## 🚀 Руководство по развертыванию

### Быстрый старт

#### Для удаленного сервера (рекомендуется)

```bash
# Первое развертывание
./scripts/deploy.sh deploy --remote

# Обновление приложения
./scripts/deploy.sh update --remote

# Проверка статуса
./scripts/deploy.sh status --remote

# Просмотр логов
./scripts/deploy.sh logs --remote
```

#### Локальное развертывание

```bash
# Первое развертывание
./scripts/deploy.sh deploy

# Обновление приложения
./scripts/deploy.sh update
```

### Основные команды развертывания

| Команда | Описание | Пример |
|---------|----------|---------|
| `deploy` | Полное развертывание | `./scripts/deploy.sh deploy --remote` |
| `update` | Быстрое обновление | `./scripts/deploy.sh update --remote` |
| `status` | Статус сервисов | `./scripts/deploy.sh status --remote` |
| `logs` | Просмотр логов | `./scripts/deploy.sh logs api --remote` |
| `restart` | Перезапуск | `./scripts/deploy.sh restart --remote` |
| `backup` | Резервная копия БД | `./scripts/deploy.sh backup --remote` |
| `rollback` | Откат к предыдущей версии | `./scripts/deploy.sh rollback --remote` |

### Настройка сервера

Скрипт автоматически подключается к серверу:
- **IP**: **************
- **Пользователь**: root
- **Путь проекта**: /opt/sms_proxy

### Что делает команда `update`

1. **Создает резервную копию** текущей версии
2. **Копирует обновленный код** на сервер
3. **Останавливает только API сервис** (БД продолжает работать)
4. **Пересобирает образ** с новым кодом
5. **Запускает обновленный сервис**
6. **Проверяет статус** и доступность API

### 🛡️ Защита от конфликтов миграций

#### ✅ Что исправлено:
- **Автоматическая проверка конфликтов** - система сама обнаруживает дублирующие колонки
- **Интеллектуальные миграции** - проверяют существование объектов перед созданием
- **Резервное копирование** - автоматически создается при каждом обновлении
- **Синхронизация истории** - автоматически помечает существующие миграции

#### 🔧 Улучшенные компоненты:

**1. Скрипт инициализации БД** (`scripts/init_db_docker.py`)
- ✅ Проверяет конфликты схемы перед применением миграций
- ✅ Автоматически синхронизирует историю миграций
- ✅ Устойчив к дублирующим колонкам
- ✅ Откатывается к прямому созданию таблиц при проблемах

**2. Миграции Alembic** (директория `alembic/versions/`)
- ✅ Все проблемные миграции исправлены с проверками `IF NOT EXISTS`
- ✅ Защита от ошибок дублирования колонок
- ✅ Безопасное добавление/удаление полей

**3. Универсальный скрипт развертывания** (`scripts/deploy.sh`)
- ✅ Поддерживает удаленное развертывание
- ✅ Автоматическое резервное копирование
- ✅ Проверка синхронизации миграций
- ✅ Детальная диагностика проблем

---

## 📊 База данных

### Схема базы данных

#### Таблица `users` (Пользователи)

| Поле | Тип | Описание |
|------|-----|----------|
| id | SERIAL (PK) | Уникальный идентификатор пользователя |
| username | VARCHAR(100) | Логин пользователя |
| email | VARCHAR(255) | Email пользователя |
| api_key | VARCHAR(64) | API-ключ для доступа |
| balance | NUMERIC(10,2) | Текущий баланс |
| role | VARCHAR(20) | Роль: user, admin |
| is_active | BOOLEAN | Статус активности |
| created_at | TIMESTAMP | Дата регистрации |
| updated_at | TIMESTAMP | Дата обновления |

**Ограничения:**
```sql
CHECK (balance >= 0)
```

#### Таблица `activations` (Активации)

| Поле | Тип | Описание |
|------|-----|----------|
| id | UUID (PK) | Уникальный ID активации |
| user_id | INT (FK) | Ссылка на пользователя |
| provider | VARCHAR | Провайдер (Firefox/SMSLive) |
| provider_ref | VARCHAR | ID активации у провайдера |
| country_id | INT (FK) | Страна номера |
| service_id | INT (FK) | Сервис активации |
| phone_number | VARCHAR | Выданный номер |
| status | VARCHAR | Статус активации |
| sms_code | VARCHAR | Полученный SMS код |
| ordered_at | TIMESTAMP | Время заказа |
| completed_at | TIMESTAMP | Время завершения |
| cost | NUMERIC(10,3) | Стоимость активации |

#### Таблица `transactions` (Транзакции)

| Поле | Тип | Описание |
|------|-----|----------|
| id | SERIAL (PK) | ID транзакции |
| user_id | INT (FK) | Пользователь |
| amount | NUMERIC(10,2) | Сумма изменения |
| type | VARCHAR | Тип: TOPUP, CHARGE, REFUND |
| related_activation | UUID (FK) | Связанная активация |
| timestamp | TIMESTAMP | Время транзакции |
| comment | VARCHAR | Комментарий |

#### Справочные таблицы

**countries** - Список стран
**services** - Список сервисов
**prices** - Цены по стране и сервису
**logs** - Журнал событий системы

---

## 🔐 Безопасность и мониторинг

### Административные эндпоинты безопасности

- `GET /api/admin/security/health-report` - Отчет о состоянии системы
- `GET /api/admin/security/user-analysis/{user_id}` - Анализ активности пользователя
- `GET /api/admin/security/balance-audit/{user_id}` - Аудит целостности баланса
- `POST /api/admin/security/log-event` - Ручное логирование событий

### Логирование

Все операции с балансом логируются:

```python
log_entry = Log(
    user_id=user_id,
    action="BALANCE_RESERVE",
    details=f"Зарезервировано {amount} для активации {activation_id}"
)
```

**Типы логов:**
- `BALANCE_RESERVE` - Резервирование средств
- `BALANCE_CHARGE` - Списание средств
- `BALANCE_REFUND` - Возврат средств
- `BALANCE_TOPUP` - Пополнение баланса
- `RETRY_OPERATION` - Повторные попытки
- `SECURITY_*` - События безопасности

### Мониторинг

#### Автоматические проверки:
- ✅ **Здоровье API** - проверяется доступность endpoint
- ✅ **Состояние БД** - проверяется подключение к PostgreSQL
- ✅ **Синхронизация миграций** - проверяется корректность истории
- ✅ **Статус контейнеров** - мониторинг Docker сервисов

#### Метрики для отслеживания:
1. Количество retry операций
2. Частота rate limiting
3. Время выполнения операций
4. Количество deadlocks
5. Подозрительная активность

#### Алерты:
1. Превышение 10% retry операций
2. Более 100 rate limit в минуту
3. Обнаружение подозрительной активности
4. Нарушение целостности баланса

---

## 🔧 Устранение неполадок

### Диагностика проблем

#### Команды для проверки состояния:

```bash
# Проверка миграций
ssh root@************** "cd /opt/sms_proxy && docker-compose exec api alembic current"

# Проверка логов
ssh root@************** "cd /opt/sms_proxy && docker-compose logs api --tail=50"

# Проверка здоровья сервисов
./scripts/deploy.sh status --remote
```

### Если появляются ошибки миграций:

1. **Проверьте текущее состояние:**
   ```bash
   ssh root@************** "cd /opt/sms_proxy && docker-compose exec api alembic history --verbose"
   ```

2. **Синхронизируйте историю:**
   ```bash
   ssh root@************** "cd /opt/sms_proxy && docker-compose exec api alembic stamp head"
   ```

3. **Повторите обновление:**
   ```bash
   ./scripts/deploy.sh update --remote
   ```

### Экстренное восстановление

#### Откат к предыдущей версии:
```bash
./scripts/deploy.sh rollback --remote
```

#### Полное восстановление:
```bash
./scripts/deploy.sh deploy --force --remote
```

### Проблемы с подключением

```bash
# Проверить соединение
ssh root@**************

# Если проблемы с SSH ключом, используйте пароль
./scripts/deploy.sh status --remote
```

### Просмотр логов

```bash
# Все сервисы
./scripts/deploy.sh logs --remote

# Только API
./scripts/deploy.sh logs api --remote

# Только БД
./scripts/deploy.sh logs postgres --remote
```

---

## 📈 Производительность

### Влияние защиты на производительность

1. **Пессимистические блокировки**: +10-20мс на операцию
2. **Rate limiting**: +1-2мс на запрос
3. **Валидация**: +1мс на операцию
4. **Retry механизм**: Только при конфликтах

### Оптимизация

1. Используйте connection pooling
2. Настройте таймауты блокировок
3. Мониторьте deadlocks
4. Оптимизируйте индексы БД

---

## 🎯 Рекомендации

### Для разработчиков:
1. **Всегда тестируйте миграции локально** перед отправкой на сервер
2. **Используйте проверки существования** в новых миграциях
3. **Делайте инкрементальные обновления** вместо больших изменений схемы

### Для администраторов:
1. **Используйте только команду `update`** для обычных обновлений
2. **Следите за логами** во время обновления
3. **Делайте резервные копии** перед критическими изменениями

---

## ✅ Заключение

Система SMS Proxy Service обеспечивает:

- **Атомарность операций** - Пессимистические блокировки
- **Целостность данных** - Ограничения БД и валидация
- **Защита от спама** - Rate limiting
- **Устойчивость к сбоям** - Retry механизм
- **Мониторинг** - Логирование и аудит
- **Безопасность** - Обнаружение подозрительной активности

**🎉 Система SMS Proxy готова к работе при высокой нагрузке и защищена от основных проблем concurrent доступа!**
