# Удаление устаревших настроек провайдеров

## Обзор изменений

После внедрения системы динамических провайдеров были удалены все устаревшие переменные окружения и настройки, связанные с провайдерами Firefox и SMSLive. Теперь все провайдеры управляются через административную панель.

## Удаленные переменные окружения

### Из файлов конфигурации (.env, .env.example, .env.production):
```bash
# Удалены следующие переменные:
FIREFOX_API_URL=http://www.firefox.fun/yhapi.ashx
FIREFOX_API_NAME=your_api_name
FIREFOX_PASSWORD=your_password
SMSLIVE_API_URL=https://api.smslive.pro/stubs/handler_api.php
SMSLIVE_API_KEY=your_api_key
DEFAULT_PROVIDER=both
AUTO_CANCEL_MINUTES=10
```

### Из docker-compose.yml:
```yaml
# Удалены переменные окружения:
- FIREFOX_API_NAME=${FIREFOX_API_NAME}
- FIREFOX_PASSWORD=${FIREFOX_PASSWORD}
- SMSLIVE_API_KEY=${SMSLIVE_API_KEY}
```

## Удаленные поля из моделей

### app/core/config.py - Settings класс:
```python
# Удалены поля:
firefox_api_url: str
firefox_api_name: str
firefox_password: str
smslive_api_url: str
smslive_api_key: str
default_provider: str
```

### app/models/setting.py - Setting модель:
```python
# Удалены поля:
firefox_api_name: Mapped[Optional[str]]
firefox_password: Mapped[Optional[str]]
smslive_api_key: Mapped[Optional[str]]
default_provider: Mapped[str]
```

### app/schemas/setting.py - Setting схемы:
```python
# Удалены поля из всех схем:
firefox_api_name: Optional[str]
firefox_password: Optional[str]
smslive_api_key: Optional[str]
default_provider: Optional[str]
```

## Обновленная логика

### Административная панель (app/api/admin.py):
- Удален маппинг устаревших полей в функции `update_env_file()`
- Оставлены только системные настройки: `debug_mode`, `log_level`

### Скрипты инициализации:
- `scripts/init_db_docker.py`: удалены устаревшие поля из создания настроек по умолчанию
- `scripts/load_provider_data.py`: заменена логика загрузки данных на использование динамических провайдеров

## Миграция базы данных

Создана и применена миграция `60a55eb5d937_remove_deprecated_provider_settings_fields.py`:

```python
def upgrade() -> None:
    """Удаляем устаревшие поля провайдеров из таблицы настроек"""

    # Удаляем поля API ключей провайдеров
    op.drop_column('settings', 'firefox_api_name')
    op.drop_column('settings', 'firefox_password')
    op.drop_column('settings', 'smslive_api_key')
    op.drop_column('settings', 'default_provider')
```

## Новая система управления провайдерами

### Где теперь управлять провайдерами:
1. **Административная панель**: `/admin` → Провайдеры
2. **API эндпоинты**: `/api/v1/providers/`
3. **База данных**: таблица `sms_providers`

### Преимущества новой системы:
- ✅ Динамическое добавление/удаление провайдеров без перезапуска
- ✅ Шифрование учетных данных в базе данных
- ✅ Приоритизация провайдеров
- ✅ Мониторинг состояния провайдеров
- ✅ Поддержка разных форматов API (Firefox, SMSActivate)
- ✅ Централизованное управление через веб-интерфейс

## Совместимость

### Что осталось без изменений:
- ✅ Все клиентские API остались совместимыми
- ✅ Формат ответов не изменился
- ✅ Логика активаций работает как прежде
- ✅ Пользовательские данные сохранены

### Что изменилось:
- ⚠️ Настройки провайдеров теперь только в административной панели
- ⚠️ Переменные окружения провайдеров больше не используются
- ⚠️ Требуется настройка провайдеров через админку после деплоя

## Инструкция по настройке после обновления

1. **Войти в административную панель**: `/admin`
2. **Перейти в раздел "Провайдеры"**
3. **Добавить провайдеров**:
   - Firefox: указать URL, логин, пароль
   - SMSLive: указать URL, API ключ
4. **Настроить приоритеты** (меньше число = выше приоритет)
5. **Протестировать соединение** через кнопку "Тест"
6. **Активировать провайдеров**

## Файлы, которые больше не нужны

Следующие переменные можно удалить из всех .env файлов:
```bash
FIREFOX_API_URL
FIREFOX_API_NAME
FIREFOX_PASSWORD
SMSLIVE_API_URL
SMSLIVE_API_KEY
DEFAULT_PROVIDER
AUTO_CANCEL_MINUTES
```

## Логирование изменений

Все изменения настроек провайдеров теперь логируются в таблице `logs` с подробной информацией о том, кто и когда внес изменения.

## Безопасность

- 🔒 Учетные данные провайдеров шифруются в базе данных
- 🔒 Доступ к управлению провайдерами только у администраторов
- 🔒 Логирование всех действий с провайдерами
- 🔒 Маскировка паролей и ключей в логах
