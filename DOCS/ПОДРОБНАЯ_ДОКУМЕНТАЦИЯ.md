# Подробная техническая документация SMS Proxy Service

## Обзор архитектуры

SMS Proxy Service - это высоконагруженный микросервис для интеграции с провайдерами виртуальных SMS номеров. Система построена на принципах Clean Architecture с четким разделением ответственности между слоями.

```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP Layer (FastAPI)                     │
├─────────────────────────────────────────────────────────────┤
│     API Controllers     │    Admin Panel    │  Middleware   │
├─────────────────────────────────────────────────────────────┤
│                    Business Logic Layer                     │
├─────────────────────────────────────────────────────────────┤
│  Services  │  Providers  │  Validation  │  Security        │
├─────────────────────────────────────────────────────────────┤
│                     Data Layer                              │
├─────────────────────────────────────────────────────────────┤
│   Models   │  Schemas   │  Database   │   Migrations        │
└─────────────────────────────────────────────────────────────┘
```

---

## 🚀 Главный модуль приложения

### `main.py` - Точка входа приложения

**Назначение:** Инициализация и настройка FastAPI приложения

**Что делает:**
- Создает экземпляр FastAPI с полной документацией API
- Настраивает middleware для безопасности и логирования
- Регистрирует все роутеры (API эндпоинты)
- Обрабатывает lifecycle события (запуск/остановка)
- Настраивает CORS и trusted hosts
- Подключает статические файлы для админки

**Как это делает:**
```python
# Lifecycle управление
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: проверка БД, инициализация
    # Shutdown: закрытие соединений
```

**Ключевые компоненты:**
- **Health Check** (`/health`) - мониторинг состояния сервиса
- **Глобальные обработчики исключений** - единообразная обработка ошибок
- **CORS и безопасность** - настройка для продакшн окружения
- **Административные эндпоинты** - авторизация админки через API ключи

---

## 🔧 Модуль Core - Ядро системы

### `app/core/config.py` - Конфигурация

**Назначение:** Централизованное управление настройками приложения

**Что делает:**
- Загружает переменные окружения через Pydantic Settings
- Валидирует конфигурацию при запуске
- Предоставляет единую точку доступа к настройкам

**Основные настройки:**
```python
class Settings(BaseSettings):
    # Приложение
    debug: bool = False
    secret_key: str

    # Настройки базы данных
    database_url: str

    # Провайдеры SMS - управляются через административную панель
    # Настройки провайдеров перенесены в таблицу sms_providers
    # Используйте /admin -> Провайдеры для управления

    # Система
    sms_timeout: int = 30  # Таймаут для SMS операций
```

### `app/core/database.py` - Работа с БД

**Назначение:** Настройка асинхронного подключения к PostgreSQL

**Что делает:**
- Создает async engine для SQLAlchemy
- Настраивает session factory
- Предоставляет dependency injection для сессий БД

**Как это работает:**
```python
# Асинхронный движок
engine = create_async_engine(settings.database_url, echo=False)

# Фабрика сессий
async_session_maker = async_sessionmaker(engine, class_=AsyncSession)

# DI для FastAPI
async def get_database_session() -> AsyncSession:
    async with async_session_maker() as session:
        yield session
```

### `app/core/logger.py` - Система логирования

**Назначение:** Структурированное JSON логирование для мониторинга

**Что делает:**
- Настраивает structlog для структурированных логов
- Создает единообразные логи во всех модулях
- Интегрируется с системами мониторинга

**Возможности:**
- JSON формат для ELK стека
- Контекстные логи с метаданными
- Разные уровни логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Автоматическое логирование исключений

### `app/core/security.py` - Безопасность

**Назначение:** Управление аутентификацией и авторизацией

**Что делает:**
- Генерирует и валидирует API ключи (64-символьные)
- Проверяет права доступа администраторов
- Обеспечивает dependency injection для защищенных эндпоинтов

**Ключевые функции:**
```python
def generate_api_key() -> str:
    """Генерация криптографически стойкого API ключа"""

async def get_current_admin_by_header(api_key: str) -> User:
    """Проверка прав администратора по API ключу"""
```

### `app/core/middleware.py` - Middleware компоненты

**Назначение:** Обработка HTTP запросов на уровне middleware

**Что делает:**
- **RateLimitMiddleware** - защита от DDoS (100 запросов/минуту на IP)
- **SecurityHeadersMiddleware** - безопасные HTTP заголовки
- **RequestLoggingMiddleware** - логирование всех запросов

**Защита от высокой нагрузки:**
```python
# Rate limiting по IP и пользователю
user_limits = {user_id: 10}  # 10 запросов/минуту на пользователя
ip_limits = {ip: 100}        # 100 запросов/минуту на IP
```

---

## 📊 Модуль Models - Модели данных

### `app/models/base.py` - Базовый класс

**Назначение:** Общий функционал для всех моделей

### `app/models/user.py` - Модель пользователя

**Назначение:** Управление пользователями и их балансом

**Что хранит:**
```python
class User(Base):
    id: int                    # Уникальный ID
    username: str              # Логин пользователя
    email: str                 # Email (опционально)
    api_key: str              # API ключ (64 символа)
    balance: Decimal          # Баланс пользователя
    role: str                 # Роль (user, admin)
    is_active: bool           # Активность аккаунта
    created_at: datetime      # Дата создания
    updated_at: datetime      # Дата обновления
    last_login: datetime      # Последний вход
```

**Защита целостности:**
- **Валидация баланса** на уровне модели
- **Ограничения БД** `CHECK (balance >= 0)`
- **Автоматические временные метки**

### `app/models/activation.py` - Модель активации

**Назначение:** Хранение информации об активациях номеров

**Что хранит:**
```python
class Activation(Base):
    id: int                   # Уникальный ID активации
    user_id: int             # ID пользователя
    provider: str            # Провайдер (firefox, smslive)
    provider_ref: str        # ID в системе провайдера
    country_id: int          # ID страны
    service_id: int          # ID сервиса
    phone_number: str        # Номер телефона
    cost: Decimal           # Стоимость активации
    status: str             # Статус (WAIT_SMS, SMS_RECEIVED, CANCELED)
    sms_code: str           # Полученный SMS код
    charged: bool           # Списаны ли средства
    created_at: datetime    # Время создания
    expires_at: datetime    # Время истечения
```

**Статусы активации:**
- `WAIT_SMS` - ожидание SMS
- `SMS_RECEIVED` - SMS получен
- `CANCELED` - отменена
- `TIMEOUT` - истек таймаут

### `app/models/transaction.py` - Модель транзакций

**Назначение:** Аудит всех финансовых операций

**Что хранит:**
```python
class Transaction(Base):
    id: int                  # ID транзакции
    user_id: int            # ID пользователя
    type: TransactionType   # Тип (DEBIT, CREDIT, REFUND)
    amount: Decimal         # Сумма операции
    description: str        # Описание операции
    activation_id: int      # Связанная активация
    created_at: datetime    # Время операции
```

### Остальные модели

- **`service.py`** - Справочник сервисов (Telegram, WhatsApp, VK)
- **`country.py`** - Справочник стран
- **`price.py`** - Тарифы на услуги по странам
- **`log.py`** - Логи действий пользователей
- **`setting.py`** - Системные настройки

---

## 🔄 Модуль Services - Бизнес логика

### `app/services/activation_service.py` - Управление активациями

**Назначение:** Основная бизнес-логика работы с активациями

**Что делает:**
- Создает новые активации номеров
- Проверяет статус SMS у провайдеров
- Управляет жизненным циклом активаций
- Списывает средства с баланса пользователей

**Ключевые методы:**

#### `create_activation()` - Создание активации
```python
async def create_activation(
    user_id: int,
    service_code: str,
    country_code: Optional[int] = None,
    operator: Optional[str] = None,
    max_price: Optional[float] = None,
) -> Dict[str, Any]:
```

**Логика работы:**
1. **Валидация** - проверка пользователя, сервиса, страны
2. **Получение цены** - определение стоимости услуги
3. **Проверка баланса** - достаточность средств (БЕЗ резервирования!)
4. **Заказ номера** - запрос к провайдерам через ProviderManager
5. **Создание записи** - сохранение активации в БД со статусом `WAIT_SMS`

#### `get_activation_status()` - Проверка статуса
```python
async def get_activation_status(
    activation_id: str,
    user_id: int,
    ip_address: str = None
) -> Dict[str, Any]:
```

**Логика работы:**
1. **Получение активации** из БД
2. **Быстрый возврат** если SMS уже получен
3. **Запрос к провайдеру** на получение SMS
4. **Списание средств** при получении SMS
5. **Обновление статуса** в БД

#### `cancel_activation()` - Отмена активации
**Логика:** Отмена у провайдера + возврат средств если они были списаны

#### `request_retry()` - Повторный запрос SMS
**Логика:** Запрос на повторную отправку SMS через провайдера

**Защита от Race Conditions:**
```python
# Пессимистическая блокировка при списании средств
user = await self.db.execute(
    select(User).where(User.id == user_id).with_for_update()
)
```

### `app/services/balance_service.py` - Управление балансом

**Назначение:** Безопасная работа с балансом пользователей

**Что делает:**
- Списывает средства с гарантией атомарности
- Пополняет баланс пользователей
- Ведет аудит всех финансовых операций

**Ключевые методы:**

#### `deduct_balance()` - Списание средств
```python
async def deduct_balance(
    user_id: int,
    amount: Decimal,
    description: str = None,
    activation_id: int = None
) -> bool:
```

**Защитные механизмы:**
1. **SELECT FOR UPDATE** - пессимистическая блокировка
2. **Проверка баланса** в рамках транзакции
3. **Атомарное обновление** баланса и создание транзакции
4. **Retry механизм** при конфликтах блокировок

#### `add_balance()` - Пополнение баланса
**Логика:** Аналогично списанию, но с увеличением баланса

### `app/services/user_service.py` - Управление пользователями

**Назначение:** CRUD операции с пользователями

**Что делает:**
- Создает новых пользователей с уникальными API ключами
- Аутентификация по API ключам
- Обновление профилей пользователей

### `app/services/price_service.py` - Управление тарифами

**Назначение:** Работа с ценами на услуги

### `app/services/mapping_service.py` - Маппинг кодов

**Назначение:** Преобразование кодов между системами

**Что делает:**
- Преобразует коды сервисов SMSActivate в коды провайдеров
- Преобразует коды стран между системами
- Загружает маппинги из файла `mappings.txt`

**Пример маппинга:**
```
# Сервисы: SMSActivate -> Firefox
tg -> 1000    # Telegram
wa -> 10001    # WhatsApp
vk -> 1008     # ВКонтакте

# Страны: SMSActivate -> Firefox
0 -> rus      # Россия
1 -> ukr      # Украина
```

---

## 🌐 Модуль Providers - Интеграция с SMS провайдерами

### `app/providers/manager.py` - Менеджер провайдеров

**Назначение:** Управление несколькими провайдерами SMS

**Что делает:**
- Объединяет работу с разными провайдерами
- Автоматически переключается при недоступности
- Балансирует нагрузку между провайдерами

**Логика работы:**
```python
async def get_number(
    service_code: str,
    country_code: Optional[int] = None,
    max_price: Optional[float] = None,
    operator: Optional[str] = None
) -> Dict[str, Any]:
    # Перебираем провайдеров в порядке приоритета
    for provider_type in self.enabled_providers:
        try:
            provider = await self.factory.create_provider(provider_type)
            result = await provider.get_number(...)
            if result.get("success"):
                return result
        except Exception:
            continue  # Пробуем следующего провайдера

    return {"success": False, "error": "NO_NUMBERS"}
```

### `app/providers/firefox.py` - Провайдер Firefox SMS

**Назначение:** Интеграция с API Firefox SMS

**Что делает:**
- Авторизация в Firefox API через логин/пароль
- Получение виртуальных номеров
- Получение SMS кодов
- Отмена активаций

**Особенности Firefox API:**
```python
# Авторизация
params = {
    "act": "login",
    "ApiName": self.api_name,
    "PassWord": self.password
}

# Получение номера
params = {
    "act": "getPhone",
    "token": self._token,
    "iid": firefox_service_id,  # Внутренний ID сервиса Firefox
    "country": firefox_country_code
}

# Получение SMS
params = {
    "act": "getPhoneCode",
    "token": self._token,
    "pkey": activation_id
}
```

**Кеширование токенов:**
```python
# Глобальный кеш токенов на 1 час
_firefox_token_cache = {
    "token": None,
    "expires_at": None,
    "api_name": None
}
```

### `app/providers/smslive.py` - Провайдер SMSLive

**Назначение:** Интеграция с API SMSLive

**Что делает:**
- Работа с SMSLive API (совместимый с SMSActivate)
- Поддержка операторов и максимальной цены
- Получение балансов и статистики

**Особенности SMSLive API:**
```python
# Получение номера
params = {
    "action": "getNumber",
    "api_key": self.api_key,
    "service": service_code,
    "country": country_code,
    "operator": operator,      # Поддержка операторов
    "maxPrice": max_price      # Поддержка лимита цены
}

# Получение статуса
params = {
    "action": "getStatus",
    "api_key": self.api_key,
    "id": activation_id
}
```

### `app/providers/factory.py` - Фабрика провайдеров

**Назначение:** Создание экземпляров провайдеров

**Что делает:**
- Инстанцирует правильный класс провайдера
- Настраивает подключения и аутентификацию
- Обеспечивает единообразный интерфейс

### `app/providers/validator.py` - Валидация

**Назначение:** Проверка ответов от провайдеров

---

## 🌐 Модуль API - Веб интерфейс

### `app/api/sms_activate.py` - Публичный API

**Назначение:** Основной API эндпоинт для клиентов

**Что делает:**
- Обрабатывает запросы по протоколу SMSActivate
- Аутентификация по API ключам
- Преобразует ответы в нужный формат

**Поддерживаемые действия:**

#### `getBalance` - Получение баланса
```http
GET /stubs/handler_api.php?api_key=KEY&action=getBalance
```
**Ответ:** `ACCESS_BALANCE:1000.50`

#### `getNumber` - Заказ номера
```http
GET /stubs/handler_api.php?api_key=KEY&action=getNumber&service=tg&country=0
```
**Ответ:** `ACCESS_NUMBER:12345:+79123456789`

#### `getStatus` - Статус активации
```http
GET /stubs/handler_api.php?api_key=KEY&action=getStatus&id=12345
```
**Ответы:**
- `STATUS_WAIT_CODE` - ожидание SMS
- `STATUS_OK:12345` - SMS получен с кодом 12345
- `STATUS_CANCEL` - активация отменена

#### `setStatus` - Управление активацией
```http
GET /stubs/handler_api.php?api_key=KEY&action=setStatus&id=12345&status=8
```
**Статусы:**
- `1` - завершить успешно
- `6` - повторный запрос SMS
- `8` - отменить активацию

### `app/api/admin.py` - Административный API

**Назначение:** Административная панель и API для управления

**Что делает:**
- Управление пользователями и их балансами
- Просмотр всех активаций и транзакций
- Настройка системы и провайдеров
- Аналитика и отчеты безопасности

**Основные разделы:**

#### Управление пользователями
- `GET /api/admin/users` - список пользователей
- `POST /api/admin/users` - создание пользователя
- `PUT /api/admin/users/{id}/balance` - изменение баланса

#### Мониторинг активаций
- `GET /api/admin/activations` - все активации
- `GET /api/admin/activations/{id}` - детали активации

#### Безопасность и аудит
- `GET /api/admin/security/health-report` - отчет безопасности
- `GET /api/admin/security/user-analysis/{id}` - анализ пользователя

#### Настройки системы
- `GET /api/admin/settings` - системные настройки
- `PUT /api/admin/settings` - обновление настроек

---

## 📋 Модуль Schemas - Валидация данных

### `app/schemas/` - Pydantic схемы

**Назначение:** Валидация и сериализация данных API

**Что включает:**
- **Входящие данные** - валидация запросов клиентов
- **Исходящие данные** - сериализация ответов
- **Внутренние схемы** - передача данных между слоями

**Примеры схем:**
```python
# Создание активации
class ActivationCreate(BaseModel):
    service_code: str
    country_code: Optional[int] = None
    operator: Optional[str] = None

# Ответ активации
class ActivationResponse(BaseModel):
    id: int
    phone_number: str
    status: str
    cost: Decimal
```

---

## 🛠 Модуль Utils - Вспомогательные утилиты

### `app/utils/security_monitor.py` - Мониторинг безопасности

**Назначение:** Обнаружение подозрительной активности

**Что делает:**
- Отслеживает частоту запросов пользователей
- Обнаруживает аномалии в поведении
- Генерирует алерты для администраторов

### `app/utils/request_utils.py` - Утилиты запросов

**Назначение:** Работа с HTTP запросами

### `app/utils/sms_parser.py` - Парсинг SMS

**Назначение:** Извлечение кодов из SMS текстов

### `app/utils/validators.py` - Валидаторы

**Назначение:** Дополнительные проверки данных

---

## 🗃 Система миграций - Alembic

### `alembic/` - Миграции базы данных

**Назначение:** Версионирование схемы базы данных

**Что включает:**
- Скрипты миграций для изменения структуры БД
- История изменений схемы
- Возможность отката изменений

**Использование:**
```bash
# Создание миграции
alembic revision --autogenerate -m "Описание изменений"

# Применение миграций
alembic upgrade head

# Откат
alembic downgrade -1
```

---

## 🔐 Архитектура безопасности

### Многоуровневая защита от Race Conditions

1. **Пессимистические блокировки (SELECT FOR UPDATE)**
   - Блокировка строки пользователя на время транзакции
   - Предотвращение одновременного доступа к балансу

2. **Атомарные операции на уровне БД**
   - Проверка и обновление баланса в одной операции
   - Защита от race conditions на уровне базы данных

3. **Ограничения целостности БД**
   ```sql
   CHECK (balance >= 0)
   CHECK (reserved_balance >= 0)
   CHECK (balance >= reserved_balance)
   ```

4. **Rate Limiting**
   - **Пользовательский**: 10 запросов в минуту на пользователя
   - **IP-адрес**: 100 запросов в минуту на IP
   - Защита от спама и DDoS атак

5. **Retry механизм**
   - Автоматический повтор при конфликтах блокировок
   - Экспоненциальная задержка: 100мс → 200мс → 400мс
   - Максимум 3 попытки

### Аутентификация и авторизация

- **API ключи**: Уникальные 64-символьные ключи для каждого пользователя
- **Отсутствие паролей**: Система полностью основана на API ключах
- **Роли пользователей**: `user` и `admin` с разными правами доступа

---

## 📊 Мониторинг и логирование

### Структурированное логирование

- **JSON формат** для интеграции с ELK стеком
- **Контекстные логи** с метаданными пользователя и операции
- **Разные уровни** логирования для разных окружений

### Health Check

- **Проверка БД**: `SELECT 1` для проверки подключения
- **Метрики сервиса**: версия, статус, время работы
- **Интеграция с мониторингом**: Prometheus, Grafana

### Аудит безопасности

- **Логирование действий**: все операции пользователей записываются
- **Аномалии поведения**: обнаружение подозрительной активности
- **Отчеты безопасности**: регулярные проверки целостности системы

---

## 🚀 Производительность и масштабирование

### Оптимизации базы данных

- **Индексы**: на часто используемых полях (api_key, user_id)
- **Партиционирование**: логов по датам
- **Connection pooling**: асинхронные пулы соединений

### Кеширование

- **Токены провайдеров**: кеширование токенов аутентификации
- **Настройки системы**: кеширование конфигурации
- **Результаты запросов**: кеширование цен и доступности

### Асинхронная архитектура

- **FastAPI**: полностью асинхронный веб-фреймворк
- **SQLAlchemy**: асинхронная работа с базой данных
- **HTTP клиенты**: асинхронные запросы к провайдерам

---

## 🔄 Жизненный цикл активации

```mermaid
graph TD
    A[Запрос номера] --> B[Проверка баланса]
    B --> C{Достаточно средств?}
    C -->|Нет| D[NO_BALANCE]
    C -->|Да| E[Запрос к провайдеру]
    E --> F{Номер получен?}
    F -->|Нет| G[NO_NUMBERS]
    F -->|Да| H[Создание активации]
    H --> I[WAIT_SMS]
    I --> J[Проверка SMS]
    J --> K{SMS получен?}
    K -->|Нет| I
    K -->|Да| L[Списание средств]
    L --> M[SMS_RECEIVED]
    I --> N[Таймаут]
    N --> O[TIMEOUT]
    I --> P[Отмена]
    P --> Q[CANCELED]
```

---

## 📁 Структура файлов

```
app/
├── core/                   # Ядро системы
│   ├── config.py          # Конфигурация
│   ├── database.py        # Подключение к БД
│   ├── logger.py          # Логирование
│   ├── security.py        # Безопасность
│   └── middleware.py      # HTTP middleware
├── models/                 # Модели данных
│   ├── user.py            # Пользователи
│   ├── activation.py      # Активации
│   ├── transaction.py     # Транзакции
│   └── ...
├── services/              # Бизнес логика
│   ├── activation_service.py    # Управление активациями
│   ├── balance_service.py       # Управление балансом
│   ├── user_service.py          # Управление пользователями
│   └── ...
├── providers/             # SMS провайдеры
│   ├── manager.py         # Менеджер провайдеров
│   ├── firefox.py         # Firefox SMS
│   ├── smslive.py         # SMSLive
│   └── factory.py         # Фабрика провайдеров
├── api/                   # API endpoints
│   ├── sms_activate.py    # Публичный API
│   └── admin.py           # Административный API
├── schemas/               # Pydantic схемы
├── utils/                 # Утилиты
│   ├── security_monitor.py      # Мониторинг безопасности
│   ├── request_utils.py         # HTTP утилиты
│   └── sms_parser.py           # Парсинг SMS
├── static/                # Статические файлы
└── templates/             # HTML шаблоны
```

---

## 🎯 Ключевые особенности архитектуры

1. **Clean Architecture** - четкое разделение слоев
2. **Dependency Injection** - слабая связанность компонентов
3. **Async/Await** - высокая производительность
4. **Type Hints** - безопасность типов
5. **Comprehensive Testing** - покрытие тестами
6. **Security First** - безопасность на всех уровнях
7. **Monitoring Ready** - готовность к мониторингу
8. **Scalable Design** - масштабируемая архитектура

Данная архитектура обеспечивает высокую надежность, безопасность и производительность SMS прокси-сервиса при работе с множественными провайдерами и высокой нагрузкой.
