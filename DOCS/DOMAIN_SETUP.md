# Настройка домена cock-liz.com

## Текущее состояние
- Проект настроен для работы с доменом `cock-liz.com`
- Cloudflare DNS уже настроен
- Нужно обновить SSL сертификаты для нового домена
- **Упорядочены .env файлы**: оставлены только `.env` (рабочий) и `.env.example` (шаблон)

## Настройки Cloudflare

В вашей панели Cloudflare для домена cock-liz.com должны быть следующие DNS записи:

```
Тип: A
Имя: @
Значение: 62.113.117.221
Прокси: Вк<PERSON>ю<PERSON><PERSON><PERSON> (оранжевое облако)

Тип: A
Имя: www
Значение: 62.113.117.221
Прокси: Включен (оранжевое облако)

Тип: A
Имя: api
Значение: 62.113.117.221
Прокси: Включен (оранжевое облако)
```

## SSL сертификаты

### Вариант 1: Использование Cloudflare SSL (рекомендуется)

1. В панели Cloudflare включите "Full (strict)" SSL режим
2. Сгенерируйте Origin Certificate:
   - Перейдите в SSL/TLS → Origin Server
   - Создайте новый сертификат для cock-liz.com и *.cock-liz.com
   - Скачайте сертификат и приватный ключ

3. Замените файлы:
```bash
# Сохраните сертификат в nginx/ssl/cert.pem
# Сохраните приватный ключ в nginx/ssl/key.pem
```

### Вариант 2: Let's Encrypt с Certbot

```bash
# Установите certbot на сервере
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Остановите docker-compose
docker-compose down

# Получите сертификаты
sudo certbot certonly --standalone -d cock-liz.com -d www.cock-liz.com

# Скопируйте сертификаты
sudo cp /etc/letsencrypt/live/cock-liz.com/fullchain.pem ./nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/cock-liz.com/privkey.pem ./nginx/ssl/key.pem

# Настройте права доступа
sudo chown $USER:$USER ./nginx/ssl/*.pem
chmod 600 ./nginx/ssl/key.pem
chmod 644 ./nginx/ssl/cert.pem
```

## Запуск проекта

После настройки SSL:

```bash
# Перезапустите контейнеры
docker-compose down
docker-compose up -d

# Проверьте логи
docker-compose logs -f nginx
docker-compose logs -f api
```

## Проверка работы

1. Откройте https://cock-liz.com - должна открыться главная страница API
2. Проверьте https://cock-liz.com/docs - документация Swagger
3. Проверьте https://cock-liz.com/health - статус приложения
4. Убедитесь что www.cock-liz.com редиректит на основной домен

## Настройки Cloudflare

### SSL/TLS настройки:
- SSL/TLS encryption mode: Full (strict)
- Always Use HTTPS: On
- Minimum TLS Version: 1.2

### Security настройки:
- Security Level: Medium
- Bot Fight Mode: On
- Browser Integrity Check: On

### Speed настройки:
- Auto Minify: HTML, CSS, JS - On
- Brotli: On
- Polish: Lossless

## Важные файлы

- `.env` - основной файл конфигурации (единственный используемый)
- `nginx/nginx.conf` - конфигурация nginx с поддержкой cock-liz.com
- `docker-compose.yml` - настройки контейнеров

## Мониторинг

Логи nginx находятся в папке `logs/nginx/`:
```bash
tail -f logs/nginx/access.log
tail -f logs/nginx/error.log
```

## Автоматическое развертывание домена

В скрипт `scripts/deploy.sh` добавлены новые команды для работы с доменом:

### Развертывание конфигурации домена

```bash
# Локальное развертывание конфигурации домена
./scripts/deploy.sh deploy-domain

# Развертывание конфигурации домена на сервере
./scripts/deploy.sh deploy-domain --remote
```

Эта команда автоматически:
- Проверяет наличие всех необходимых файлов домена
- Создает резервную копию текущей конфигурации
- Копирует конфигурацию nginx для cock-liz.com
- Копирует SSL сертификаты
- Обновляет переменные окружения
- Обновляет docker-compose.yml
- Проверяет конфигурацию nginx
- Перезапускает сервисы с новой конфигурацией
- Проверяет работу домена

### Проверка статуса домена

```bash
# Локальная проверка статуса домена
./scripts/deploy.sh check-domain

# Проверка статуса домена на сервере
./scripts/deploy.sh check-domain --remote
```

Эта команда проверяет:
- Наличие файлов конфигурации
- Статус SSL сертификатов
- Состояние контейнеров
- Корректность конфигурации nginx
- Доступность домена cock-liz.com
- Работу редиректов (HTTP->HTTPS, www->основной домен)
- Доступность API endpoints

### Необходимые файлы для домена

Команда `deploy-domain` проверяет наличие следующих файлов:
- `nginx/nginx.conf` - конфигурация nginx с настройками для cock-liz.com
- `nginx/ssl/cert.pem` - SSL сертификат
- `nginx/ssl/key.pem` - приватный ключ SSL
- `.env` - переменные окружения с настройками домена
- `docker-compose.yml` - конфигурация Docker Compose

### Примеры использования

```bash
# Полное развертывание проекта с доменом на сервере
./scripts/deploy.sh deploy --remote

# Быстрое обновление только конфигурации домена
./scripts/deploy.sh deploy-domain --remote

# Проверка текущего состояния домена
./scripts/deploy.sh check-domain --remote

# Просмотр логов nginx для диагностики
./scripts/deploy.sh logs nginx --remote
```

### Безопасность

- SSL сертификаты автоматически получают правильные права доступа (600 для ключа, 644 для сертификата)
- Создается резервная копия перед каждым обновлением конфигурации
- Конфигурация nginx проверяется перед применением
- В случае ошибки можно быстро откатиться к предыдущей версии

### Мониторинг домена

После развертывания рекомендуется регулярно проверять:
- Срок действия SSL сертификатов
- Доступность домена
- Логи nginx на наличие ошибок
- Состояние контейнеров

```bash
# Регулярная проверка (можно добавить в cron)
./scripts/deploy.sh check-domain --remote
```
