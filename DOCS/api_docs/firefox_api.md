API Document
Affirmation
Request url:http://www.firefox.fun/yhapi.ashx
Remark:Chinese characters are encoded using url
Method:GET
Please save the log,If you need technical assistance, you must provide the log

A return of "1|*" indicates that the request was successful, and a return of "0|*" indicates that the request failed."*" indicates information content

Login
Call it once when starting the script
Token remains unchanged without changing the account password
Request parameters:
Field	Value	Required	Remark
act	login	Required	Fixed value, cannot be modified
ApiName	Your ApiName	Required	[Look my ApiName]After login go to API--My APIName
PassWord	Your password	Required	Login password at the time of registration
Request example:http://www.firefox.fun/yhapi.ashx?act=login&ApiName=12348&PassWord=1231
Successfully returned:1|token
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	The ApiName cannot be empty
-2	The ApiName length must be between 3-30
-3	ApiName cannot contain the special symbol '|'
-4	ApiName cannot contain Chinese characters
-5	The PassWord cannot be empty
-6	The PassWord length must be between 3-30
-7	In the same IP case, the last request failed and will be retried in 1 minute
-8	ApiName has been disabled
-9	ApiName or PassWord is error
User information
Request parameters:
Field	Value	Required	Remark
act	myInfo	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
Request example:http://www.firefox.fun/yhapi.ashx?act=myInfo&token=b7c94daad5e3dd71ffca9298976ec0d4_3
Successfully returned:1|balance|level|integral
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	Token is invalid, please call the login interface
-3	Please wait 60 seconds before requesting
Get a number
Request parameters:
Field	Value	Required	Remark
act	getPhone	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
iid	Service id	Required	View on the Service list
country	Country ID		View on the Country list,Random if no input
did	Developer ID		View on developer side
dock	return docking code?		0/empty or 1；0/empty :not return，1: return
maxPrice	max price		If you don't enter or enter 0, this feature is not enabled,After entering, it will be automatically added to the exclusive matchmaking that meets the requirements
mobile	The mobile number you need		Get the specified mobile number
pushUrl	Push link		We will push SMS to you through this link,detailed description
Request example:http://www.firefox.fun/yhapi.ashx?act=getPhone&token=b7c94daad5e3dd71ffca9298976ec0d4_3&iid=1001&did=&country=&dock=&maxPrice=0&mobile=&pushUrl=
Successfully returned:1|pkey|get time|country id|area code|city|prot name|mobile number|docking code
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	There is no number for the condition you selected
-2	Token does not exist
-3	The service does not exist
-4	The country is error
-5	The service not audit
-6	The service disabled
-7	The token disabled
-8	Your balance is insufficient, please recharge
-9	Too many numbers, please supplement the balance【Actively call the release interface when unavailable】
-10	The service does not allow specifying the number
If you need to receive messages many times
Call process:Get a number-->trigger first sms-->Get sms code-->Get a number by mobile-->trigger second sms-->Get sms code-->And so on
Get sms code
Request parameters:
Field	Value	Required	Remark
act	getPhoneCode	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
Request example:http://www.firefox.fun/yhapi.ashx?act=getPhoneCode&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19
Successfully returned:1|code|sms content
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	The pkey invalid
-3	Waiting for verification code,Continue to call after 5 seconds
-4	Mobile number is unavailable, please give up its work
-5	The mobile number has been blacked, please give up its work
If no SMS is received, it will be returned as a failure
Send sms
Request parameters:
Field	Value	Required	Remark
act	sendCode	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
receiver	Receiver mobile number	Required	example:10086
smscontent	SMS content you need to send	Required	example:cxye
Request example:http://www.firefox.fun/yhapi.ashx?act=sendCode&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19&receiver=10086&smscontent=cxye
Successfully returned:1|
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	The pkey invalid
-3	The receiver cannot be empty
-4	The smscontent cannot be empty
-5	unknown error
-6	Mobile number is unavailable, please give up its work
-7	Mobile number is unavailable, please give up its work
-8	SMS is not allowed for this service
-9	No keyword ,Please contact customer service
-10	SmsContent does not conform to rules
-11	Do not submit repeatedly
Get send sms state
Use [Get sms code] to get the SMS sending receipt
Release number
Called only when no SMS is received. If the SMS is received, this interface is not required
Request parameters:
Field	Value	Required	Remark
act	setRel	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
Request example:http://www.firefox.fun/yhapi.ashx?act=setRel&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19
Successfully returned:1|
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
x	X is a positive integer, which means that it is allowed to be released after x seconds。example:0|30
-1	Token does not exist
-2	The pkey invalid
-3	Mobile number is unavailable, please give up its work
-4	SMS has been received and cannot be released
-5	SMS is being sent, release not allowed
-6	Released more than times, automatically blacked
Black number
Called only when you don't want to use this number again
Request parameters:
Field	Value	Required	Remark
act	addBlack	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
reason	Blackening reason	Required	url encoding of Chinese characters
Request example:http://www.firefox.fun/yhapi.ashx?act=addBlack&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19&reason=used
Successfully returned:1|
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	The pkey invalid
-3	The reason cannot be empty
-4	Mobile number is unavailable, please give up its work
-5	No SMS received, please call [Release number]
-6	Your account does not allow access to [Black number]
Feedback state
To call it, you need to configure permissions. The account has no permissions by default
Do not call those without permission to avoid the decrease of account balance
Request parameters:
Field	Value	Required	Remark
act	apiReturn	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
remark	Feedback state	Required	Fixed value 0:success，-1:fail，-2:No verification code，-3:The mobile number has been used
Request example:http://www.firefox.fun/yhapi.ashx?act=apiReturn&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19&remark=0
Successfully returned:1|
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	The pkey invalid
-3	The remark cannot be empty
-4	Mobile number is unavailable, please give up its work
-5	You don't have permission
Reuse
Called when the phone number needs to be used again (unspecified number)
Called after receiving the verification code
Re use will re settle the expense
Please do not call non special items to avoid the decrease of account balance
Request parameters:
Field	Value	Required	Remark
act	setAgain	Required	Fixed value, cannot be modified
token	Your token	Required	Token value returned by login
pkey	PKey value returned by get a number	Required	PKey value returned by get a number
min	How many minutes to use again		(Range 2-300 minutes),The default value is 5
Request example:http://www.firefox.fun/yhapi.ashx?act=setAgain&token=b7c94daad5e3dd71ffca9298976ec0d4_3&pkey=0524EA1B2A4DC810E3E535BE036C2C170E914ECC4D46ED19&min=5
Successfully returned:1|
Failure return:0|Failure code
Failure code description:
Failure code	Failure code description
-1	Token does not exist
-2	The pkey invalid
-3	The min value error
-4	Mobile number is unavailable, please give up its work
-5	No SMS received, please call [Release number]
Get price list
Request parameters:
Field	Value	Required	Remark
act	getItem	Required	Fixed value, cannot be modified
key	The service keyword		The service keyword
Request example:http://www.firefox.fun/yhapi.ashx?act=getItem&key=
Successfully returned:price list format json
Field Description:
Item_ID:Service id
Item_Name:Service name
Item_UPrice:Price
Country_ID:Country ID
Country_Title:Country Name