Работа с API SMSLIVE.PRO
Наш API полностью совместим с аналогичными сайтами.
Адрес для API запросов: https://api.smslive.pro/stubs/handler_api.php
Поддерживаются POST и GET запросы.
Запросы должны иметь ключ API в качестве параметра api_key.

Ваш API ключ

6j8btJbqaIPMSrJslYQEeOIPTn1uuXpX1Fr1Wdzb
Количество доступных номеров
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getNumbersStatus&country=$country
Параметры:
$api_key - Ваш ключ API
$country * - Страна
* - необязательный параметр

Ответ:
{"wa":"44","vk":"32","tg":"400","ig":"2","ya":"100","tw":"999","mt":"32"}

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие

Получение баланса
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getBalance
Параметры:
$api_key - Ваш ключ API

Ответ:
ACCESS_BALANCE:'баланс на счету'

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие

Заказ номера
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getNumber&service=$service&country=$country&ref=$ref&operators=1,2,3&exceptOperators=4,5,6&maxPrice=$maxPrice
Параметры:
$api_key - Ваш ключ API
$service - Сервис для заказа
$country * - Страна номера
$operators * - Операторы, не больше 5 шт.
$exceptOperators * - Исключаемые операторы, не больше 5 шт.
$ref * - ID аккаунта, тип аккаунта должен быть «Разработчик». подробности
$maxPrice * - максимальная цена, за которую вы готовы купить номер. как пользоваться
Если указан maxPrice параметр, то Макс. цена + API из кабинета игнорируется, для Макс. цены + API страна обязательна!
* - необязательный параметр

Ответ:
ACCESS_NUMBER:$activationId:$phoneNumber

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие
BANNED - временная блокировка
BAD_SERVICE - некорректное наименование сервиса
BAD_COUNTRY - некорректное наименование страны
BAD_MAX_PRICE - некорректная максимальная цена
BAD_OPERATORS - оператор не найден
TOO_MANY_OPERATORS - количество операторов превышает 5 шт.
BAD_EXCEPT_OPERATORS - исключаемый оператор не найден
TOO_MANY_EXCEPT_OPERATORS - количество исключаемых операторов превышает 5 шт.
NO_BALANCE - недостаточно средств на балансе
NO_CONNECTION - нет подключения к сервису
NO_NUMBERS - нет доступных номеров для заказа

Заказ номера V2
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getNumberV2&service=$service&country=$country&ref=$ref&operators=1,2,3&exceptOperators=4,5,6&maxPrice=$maxPrice
Ответ:
{
"activationId": $activationId,
"phoneNumber": $phoneNumber,
"activationCost": "5.5",
"countryCode": "0",
"activationTime": "2023-11-11 18:30:20",
}

Статус активации
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getStatus&id=$id
Параметры:
$api_key - Ваш ключ API
$id - ID активации полученный в getNumber или getNumberV2

Ответ:
STATUS_OK:'код из смс'
STATUS_WAIT_CODE - ожидание смс

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие
NO_ACTIVATION - ID активации не существует

Изменение статуса активации
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=setStatus&status=$status&id=$id
Параметры:
$api_key - Ваш ключ API
$id - ID активации полученный в getNumber или getNumberV2
$status - Статус активации

1 сообщить о готовности номера (смс на номер отправлено)
3 запросить еще один код (бесплатно)
6 завершить активацию
8 сообщить о том, что номер использован и отменить активацию

Логика работы с API
Получить номер используя метод getNumber
после этого доступны следующие действия
8 - Отменить активацию (если номер вам не подошел)
1 - Сообщить, что SMS отправлена (необязательно)

Для активации со статусом 1
8 - Отменить активацию
Сразу после получения кода:
3 - Запросить еще одну смс
6 - Подтвердить SMS-код и завершить активацию

Для активации со статусом 3
6 - Подтвердить SMS-код и завершить активацию

Ответ:
ACCESS_READY - номер готов к получению кода
ACCESS_ACTIVATION - сервис активирован
ACCESS_CANCEL - активация отменена

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие
NO_ACTIVATION - ID активации не существует
BAD_STATUS - некорректный статус
EARLY_CANCEL_DENIED - нельзя отменить номер в первые 2 минуты

Список цен
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=getPrices
Параметры:
$api_key - Ваш ключ API
$priceMap * - (true | false - по умолчанию) Полный список количества и цен
* - необязательный параметр

Ответ:
{"Код страны":{"Код сервиса":{"Цена":"Количество", "Цена":"Количество"}
{"2":{"wa":{"10.00":"10","15.00":"44"}

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие

Купить почту
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=buyMailActivation&service=$service
Параметры:
$api_key - Ваш ключ API
$service - Сервис для заказа

Ответ:
{
"status": "OK",
"activationId": $activationId,
"email": $email,
}
OR
{
"status": "ERROR",
"ERROR": "ERROR_TRY_AGAIN",
}

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие
BAD_SERVICE - некорректное наименование сервиса
ERROR_TRY_AGAIN - нет доступных почт попробуйте снова

Проверить почту
https://api.smslive.pro/stubs/handler_api.php?api_key=$api_key&action=checkMailActivation&id=$id
Параметры:
$api_key - Ваш ключ API
$id - ID активации полученный в buyMailActivation

Ответ:
{
"status": "OK",
"code": "text code",
}
OR
{
"status": "ERROR",
"ERROR": "FORBIDDEN",
}

Возможные ошибки:
BAD_KEY - неверный API-ключ
BAD_ACTION - некорректное действие
FORBIDDEN - ID активации не существует