# Архитектура прокси‑сервиса SMSActivate (FastAPI + PostgreSQL)

*Документ содержит полное проектирование сервиса‑прокси, а также **дополненный раздел** с детальной пошаговой работой с провайдером **Firefox**: от получения токена до обработки СМС.*

---

## 1. Обзор системы

*(содержимое предыдущего раздела было перенесено сюда без изменений, см. оригинальный ответ)*

---

## 2. Интеграция с провайдером **Firefox**

Ниже приведён алгоритм, по которому прокси‑сервис взаимодействует с API Firefox. Наша цель — полностью «спрятать» сложный протокол Firefox и выдать пользователю привычные ответы SMSActivate (`ACCESS_NUMBER`, `STATUS_OK`, …).

### 2.1 Авторизация и жизненный цикл **token**

| Шаг | Действие                | Детали                                                                                                     |                                                                                                                                                           |
| --- | ----------------------- | ---------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | `act=login`             | Запрос: `GET http://www.firefox.fun/yhapi.ashx?act=login&ApiName=<API_NAME>&PassWord=<PASS>`               |                                                                                                                                                           |
| 2   | Парсим ответ            | Успех: `1\|<token>`  → сохраняем `token` в таблицу `provider_tokens` (id = "Firefox", value, created\_at). |                                                                                                                                                           |
| 3   | Повторное использование | Пока код/пароль не меняются, токен бессрочный. Проверяем токен перед каждым запросом.                      |                                                                                                                                                           |
| 4   | Обработка ошибки \`0    | -2\` (token invalid)                                                                                       | Немедленно вызываем login повторно и повторяем исходный запрос (retry 1).                                                                                 |
| 5   | Защита от спама         | Код \`0                                                                                                    | -3`означает «подождите 60 сек», прокси возвращает пользователю`NO\_CONNECTION\` и ставит internal cooldown (в Redis) ≈ 65 сек, чтобы не штормить Firefox. |

### 2.2 Первоначальная загрузка справочников

* `act=getItem` без параметров → JSON‑список «*price list*».<br>
  Для каждой записи создаём/обновляем строки в таблицах **countries**, **services**, **prices**.
* Сервис‑ID Firefox (`iid`) и код страны Firefox (`country id`) кладём в mapping‑таблицу `provider_mappings`:

  ```text
  provider = "Firefox",  internal_service_id → iid,  internal_country_id → country_id
  ```

### 2.3 Заказ номера у Firefox (замена `getNumber`)

| Шаг                                                                                                     | Действие                                                                                          |
| ------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- |
| 1                                                                                                       | Пользователь вызывает: `getNumber &service=<S> &country=<C>`                                      |
| 2                                                                                                       | FastAPI‑backend → ищет price(S,C), проверяет баланс, генерирует `activation_id` (12‑симв. Base62) |
| 3                                                                                                       | Выбираем Firefox (если активен) → формируем запрос:                                               |
| `GET http://www.firefox.fun/yhapi.ashx?act=getPhone&token=<token>&iid=<iid>&country=<countryId>&dock=0` |                                                                                                   |
| *(другие поля did/maxPrice/mobile оставляем пустыми)*                                                   |                                                                                                   |
| 4                                                                                                       | Разбираем ответ:                                                                                  |

* **Успех**: `1|pkey|getTime|countryId|area|city|protName|mobile|dockCode`

  * `pkey` — внутренний ID Firefox.
  * `mobile` — локальный номер без кода страны.
  * Поле **area** для России может быть «7» или пустым — **НЕ** используем. |
    \| 5 | Формируем полный номер:

```python
full_number = f"+{calling_code[countryId]}{mobile}"
```

где `calling_code` хранится в `countries`.
\| 6 | Сохраняем запись в `activations`:
`id=activation_id`, `provider_ref=pkey`, `phone_number=full_number`, `status="WAIT_SMS"`, `ordered_at=now()`... |
\| 7 | Отдаём пользователю:
`ACCESS_NUMBER:<activation_id>:<full_number>` |
\| 8 | Запускаем background‑task: *дедлайн* = now + 10 мин (auto‑cancel). |

### 2.4 Получение SMS‑кода (`getStatus`)

```mermaid
graph TD;
  A[Client: getStatus] -->|По id| B[Proxy]
  B --> C{Status
  в activations}
  C -->|SMS_RECEIVED| R[STATUS_OK]
  C -->|CANCELED| X[STATUS_CANCEL]
  C -->|WAIT_SMS| D[Proxy → Firefox]
  D --> E[GET act=getPhoneCode&token& pkey]
  E -->|1\|code|sms| F[Save → sms_code]
  F --> R
  E -->|0\|-3 (wait)| W[STATUS_WAIT_CODE]
  E -->|0\|-4/-5 (bad)| Y[Auto cancel]
```

| Ответ Firefox | Действие прокси                             | Ответ клиенту     |                                                                           |                    |
| ------------- | ------------------------------------------- | ----------------- | ------------------------------------------------------------------------- | ------------------ |
| \`1           | <code>                                      | <msg>\`           | Обновить activations.sms\_code, status = `SMS_RECEIVED`, списать средства | `STATUS_OK:<code>` |
| \`0           | -3\`                                        | Код ещё не пришёл | `STATUS_WAIT_CODE`                                                        |                    |
| \`0           | -4`•`0                                      | -5\`              | Немедленно отменить: `setRel`, status = `CANCELED`                        | `STATUS_CANCEL`    |
| Другое 0\|х   | Маппим на `STATUS_CANCEL` + логируем ошибку | `STATUS_CANCEL`   |                                                                           |                    |

> **Важно:** Firefox просит паузу 5 сек между вызовами, иначе ‑3. В клиенте уже принято опрашивать раз в 3–5 с; мы дополнительный rate‑limit не делаем, но если нужно – используем Redis TTL‑флаг per activation.

### 2.5 Ручная и автоматическая отмена (`setStatus=8`)

* Для клиента метод SMSActivate `setStatus&id=<id>&status=8` проксируется в Firefox `act=setRel`.
* Ответы Firefox:

  * `1|` → `ACCESS_CANCEL`
  * `0|x|seconds` → отложенная отмена (x = 0) → шедулим задачу через seconds для повторной отмены.

### 2.6 Черный список номера (при рекламации)

Если пользователь после получения SMS убеждается, что номер «был использован», он может вызвать `setStatus=8` с доп. параметром `black=1` (наш кастом), или отдельное действие admin‑callback. Прокси вызывает `act=addBlack&reason=used`. Ответ `1|` → считаем номер занесённым в BL.

### 2.7 Повторное использование номера (`setAgain`)

* При запросе пользователя `setStatus&id=<id>&status=3` (получить ещё один код) → `act=setAgain&min=5`.
* Ответ `1|` → меняем activations.status=`WAIT_SMS` и продлеваем дедлайн ещё на 10 мин.

### 2.8 Таблица маппинга ошибок Firefox → SMSActivate

| Firefox code | Значение                 | Наш код                                                                                           |
| ------------ | ------------------------ | ------------------------------------------------------------------------------------------------- |
| `-1`         | Нет номера по параметрам | `NO_NUMBERS`                                                                                      |
| `-2`         | Токен не существует      | `NO_CONNECTION` *(прокси попытается перезалогиниться и ретрайнуть; если снова – отдаст BAD\_KEY)* |
| `-3`         | Сервис не существует     | `BAD_SERVICE`                                                                                     |
| `-4`         | Страна ошибка            | `BAD_COUNTRY`                                                                                     |
| `-7`         | Токен disabled           | `NO_CONNECTION`                                                                                   |
| `-8`         | Баланс Firefox пуст      | `NO_CONNECTION` *(мы переключаемся на SMSLive, если включён)*                                     |
| и т.д.       | (смотри firefox\_api.md) |                                                                                                   |

### 2.9 Псевдокод запроса номера

```python
async def request_number_firefox(user, service_id, country_id):
    token = await ensure_token_firefox()
    iid = map_service_to_iid(service_id)
    country_code = map_country_to_firefox(country_id)

    params = {
        'act': 'getPhone',
        'token': token,
        'iid': iid,
        'country': country_code,
        'dock': 0,
    }
    resp = await http_get(FIREFOX_URL, params)
    if not resp.startswith('1|'):
        raise TranslateFirefoxError(resp)  # поднимает HTTPException с нужным SMSActivate‑кодом

    parts = resp.split('|')
    pkey, mobile = parts[1], parts[7]
    full_number = f"+{calling_code[country_id]}{mobile}"

    activation = Activation.create(
        id=generate_id(), user=user, provider='Firefox', provider_ref=pkey,
        phone_number=full_number, service_id=service_id, country_id=country_id,
        status='WAIT_SMS', cost=get_price(service_id, country_id)
    )
    schedule_auto_cancel(activation.id, minutes=10)
    return f"ACCESS_NUMBER:{activation.id}:{full_number}"
```

---

## 3. Интеграция с SMSLive

*(раздел из предыдущего документа; без изменений)*

---

## 4. Бизнес‑логика, таблицы БД, пользовательское API, админ‑панель

*(полностью из оригинального документа; перенесено без изменений)*

---

## 5. Расширенные сценарии и edge‑cases

*(пункт сохранён без изменений, дополнения при необходимости)*

---

**Файл последний раз обновлён:** 24 мая 2025 г.
