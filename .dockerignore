# Скомпилированные Python файлы
__pycache__/
*.py[cod]
*$py.class
*.so

# Временные файлы
*.swp
*.swo
*~

# Git репозиторий
.git/
.gitignore
.github/

# Файлы среды разработки
.env*
.vscode/
.idea/
*.iml

# Логи
*.log
logs/

# Данные
data/
backups/

# Документация
README.md
docs/

# Скрипты развертывания
scripts/
docker-compose*.yml
Dockerfile*

# Тестовые файлы
tests/
*_test.py
test_*.py

# Системные файлы
.DS_Store
Thumbs.db

# Временные директории
tmp/
temp/

# Файлы резервных копий
app_backup_*

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.pytest_cache

# Виртуальные окружения
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Логи и данные
logs/
*.log
data/

# Документация
*.md
DOCS/

# Тестирование
.pytest_cache/
test_*.py
*_test.py
tests/

# Временные файлы
*.tmp
*.temp
.tmp/

# SSL сертификаты
nginx/ssl/

# Резервные копии
*.bak
*.backup
backups/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Скрипты развертывания
scripts/deploy.sh
scripts/init-db.sql
!scripts/docker-entrypoint.sh
!scripts/init_db_docker.py
!scripts/load_provider_data.py
