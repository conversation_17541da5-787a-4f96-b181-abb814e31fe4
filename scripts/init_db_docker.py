#!/usr/bin/env python3
"""
Скрипт для полной инициализации базы данных в Docker
Создает таблицы, применяет миграции и добавляет начальные данные
"""
from __future__ import annotations

import asyncio
import os
from pathlib import Path
import sys
import time

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import subprocess

from sqlalchemy import select, text
from sqlalchemy.exc import OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import Base, async_session_maker, engine
from app.models import Country, Price, Service, Setting, SMSProvider, User


async def wait_for_database(max_retries: int = 30, delay: int = 2):
    """Ожидание готовности базы данных"""
    print("🔄 Ожидание готовности базы данных...")

    for attempt in range(max_retries):
        try:
            async with engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            print("✅ База данных готова к работе")
            return True
        except OperationalError as e:
            print(f"⏳ Попытка {attempt + 1}/{max_retries}: База данных не готова ({e})")
            if attempt < max_retries - 1:
                time.sleep(delay)
            else:
                print("❌ Превышено время ожидания базы данных")
                return False

    return False


async def run_alembic_migrations():
    """Запуск миграций Alembic с улучшенной обработкой ошибок"""
    print("🔄 Применение миграций Alembic...")

    try:
        # Проверяем текущую версию миграций
        result = subprocess.run(
            ["alembic", "current"], capture_output=True, text=True, cwd="/app"
        )

        if result.returncode != 0:
            print("📝 Инициализация истории миграций...")
            # Проверяем есть ли уже таблицы в БД
            async with engine.begin() as conn:
                result_tables = await conn.execute(
                    text(
                        """
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public' AND table_name != 'alembic_version'
                    """
                    )
                )
                existing_tables = [row[0] for row in result_tables.fetchall()]

                if existing_tables:
                    # Если таблицы есть, помечаем текущую миграцию как выполненную
                    print(
                        "ℹ️  Обнаружены существующие таблицы, синхронизируем с миграциями..."
                    )
                    subprocess.run(["alembic", "stamp", "head"], check=True, cwd="/app")
                    print("✅ История миграций синхронизирована")
                    return True

        # Пробуем применить миграции с обработкой дублирующих колонок
        try:
            subprocess.run(["alembic", "upgrade", "head"], check=True, cwd="/app")
            print("✅ Миграции применены успешно")
            return True

        except subprocess.CalledProcessError as migration_error:
            error_output = str(migration_error)

            # Проверяем на ошибки дублирующих колонок
            if "already exists" in error_output or "DuplicateColumn" in error_output:
                print("⚠️  Обнаружен конфликт миграций (дублирующие колонки)")
                print("🔄 Попытка автоматического исправления...")

                # Помечаем проблемные миграции как выполненные
                await mark_problematic_migrations_as_applied()

                # Пробуем применить миграции снова
                try:
                    subprocess.run(
                        ["alembic", "upgrade", "head"], check=True, cwd="/app"
                    )
                    print("✅ Миграции исправлены и применены")
                    return True
                except subprocess.CalledProcessError:
                    print(
                        "⚠️  Автоматическое исправление не помогло, используем прямое создание таблиц"
                    )
                    return False
            else:
                # Другая ошибка - возвращаем False для fallback
                print(f"❌ Ошибка применения миграций: {migration_error}")
                return False

    except subprocess.CalledProcessError as e:
        print(f"❌ Ошибка инициализации миграций: {e}")
        return False


async def mark_problematic_migrations_as_applied():
    """Помечает проблемные миграции как уже применённые"""
    problematic_migrations = [
        "c38a4bc07b3d",  # add_last_login_to_users
        "c8ba4efbd5d0",  # add_last_login_and_updated_at_fields
        "e45297615241",  # remove_reserved_balance_field
    ]

    async with engine.begin() as conn:
        for migration_id in problematic_migrations:
            try:
                # Проверяем, есть ли миграция в истории
                result = await conn.execute(
                    text(
                        "SELECT version_num FROM alembic_version WHERE version_num = :version"
                    ),
                    {"version": migration_id},
                )

                if not result.fetchone():
                    # Если миграции нет в истории, добавляем её
                    await conn.execute(
                        text(
                            "INSERT INTO alembic_version (version_num) VALUES (:version) ON CONFLICT DO NOTHING"
                        ),
                        {"version": migration_id},
                    )
                    print(f"ℹ️  Миграция {migration_id} помечена как выполненная")

            except Exception as e:
                print(f"⚠️  Не удалось пометить миграцию {migration_id}: {e}")
                continue


async def create_tables_if_not_exist():
    """Создание таблиц если они не существуют"""
    print("🔄 Проверка и создание таблиц...")

    try:
        async with engine.begin() as conn:
            # Проверяем, существуют ли таблицы
            result = await conn.execute(
                text(
                    """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """
                )
            )
            existing_tables = [row[0] for row in result.fetchall()]

            if not existing_tables:
                print("📝 Создание таблиц...")
                await conn.run_sync(Base.metadata.create_all)
                print("✅ Таблицы созданы")
            else:
                print(
                    f"ℹ️  Найдено {len(existing_tables)} таблиц: {', '.join(existing_tables)}"
                )

        return True

    except Exception as e:
        print(f"❌ Ошибка создания таблиц: {e}")
        return False


async def add_initial_data():
    """Добавление начальных данных"""
    print("🔄 Добавление начальных данных...")

    async with async_session_maker() as session:
        try:
            # Проверяем, есть ли уже страны
            result = await session.execute(select(Country))
            existing_countries = result.scalars().all()

            countries = []
            if existing_countries:
                print(
                    f"ℹ️  Найдено {len(existing_countries)} стран, пропускаем добавление стран"
                )
                countries = existing_countries
            else:
                print("📝 Добавление стран...")
                # Добавляем страны
                countries_data = [(12, "США")]

                for code, name in countries_data:
                    country = Country(code=code, name=name, is_active=True)
                    session.add(country)
                    countries.append(country)

                await session.commit()
                print(f"✅ Добавлено {len(countries)} стран")

            # Проверяем, есть ли уже сервисы
            result = await session.execute(select(Service))
            existing_services = result.scalars().all()

            services = []
            if existing_services:
                print(
                    f"ℹ️  Найдено {len(existing_services)} сервисов, пропускаем добавление сервисов"
                )
                services = existing_services
            else:
                print("📝 Добавление сервисов...")
                # Добавляем сервисы
                services_data = [("tg", "Telegram")]

                for code, name in services_data:
                    service = Service(code=code, name=name, is_active=True)
                    session.add(service)
                    services.append(service)

                await session.commit()
                print(f"✅ Добавлено {len(services)} сервисов")

            # Обновляем объекты для получения ID если они новые
            if not existing_countries:
                for country in countries:
                    await session.refresh(country)
            if not existing_services:
                for service in services:
                    await session.refresh(service)

            # Проверяем, есть ли уже цены
            result = await session.execute(select(Price))
            existing_prices = result.scalars().all()

            if not existing_prices:
                print("📝 Добавление цен...")
                # Добавляем базовые цены для популярных комбинаций
                base_prices = [
                    # США
                    (12, "tg", 15.0)
                ]

                prices = []
                for country_code, service_code, price_value in base_prices:
                    # Находим страну и сервис
                    country = next(
                        (c for c in countries if c.code == country_code), None
                    )
                    service = next(
                        (s for s in services if s.code == service_code), None
                    )

                    if country and service:
                        price = Price(
                            country_id=country.id,
                            service_id=service.id,
                            price=price_value,
                            available=100,
                        )
                        session.add(price)
                        prices.append(price)

                await session.commit()
                print(f"✅ Добавлено {len(prices)} цен")
            else:
                print(
                    f"ℹ️  Найдено {len(existing_prices)} цен, пропускаем добавление цен"
                )

            # Проверяем, есть ли уже настройки
            result = await session.execute(select(Setting))
            existing_settings = result.scalars().all()

            if not existing_settings:
                print("📝 Добавление настроек...")
                # Добавляем настройки с новыми полями
                default_settings = Setting(
                    # SMS провайдеры управляются через административную панель
                    # Только системные настройки
                    debug_mode=False,
                    log_level="INFO",
                )
                session.add(default_settings)

                await session.commit()
                print("✅ Настройки добавлены")
            else:
                print(
                    f"ℹ️  Найдено {len(existing_settings)} настроек, пропускаем добавление настроек"
                )

            # Проверяем, есть ли уже пользователи
            result = await session.execute(select(User))
            existing_users = result.scalars().all()

            if not existing_users:
                print("📝 Создание пользователей...")
                import secrets
                import string

                # Генерируем сложный API ключ для администратора
                def generate_secure_api_key(length=64):
                    alphabet = string.ascii_letters + string.digits + "_-"
                    return "".join(secrets.choice(alphabet) for _ in range(length))

                admin_api_key = generate_secure_api_key()

                # Создаем тестового пользователя
                test_user = User(
                    username="test_user",
                    email="<EMAIL>",
                    api_key="test_api_key_12345678901234567890",
                    balance=1000.0,
                    role="user",
                    is_active=True,
                )
                session.add(test_user)

                # Создаем админа с сложным API ключом
                admin_user = User(
                    username="admin",
                    email=settings.admin_email,
                    api_key=admin_api_key,
                    balance=0.0,
                    role="admin",
                    is_active=True,
                )
                session.add(admin_user)

                await session.commit()
                print("✅ Пользователи созданы")
                print(f"🔑 API ключ администратора: {admin_api_key}")
                print(
                    "⚠️  СОХРАНИТЕ ЭТОТ КЛЮЧ! Он понадобится для доступа к админ-панели"
                )
            else:
                print(
                    f"ℹ️  Найдено {len(existing_users)} пользователей, пропускаем создание пользователей"
                )
                # Проверяем, есть ли администратор
                admin_users = [u for u in existing_users if u.role == "admin"]
                if admin_users:
                    print(f"ℹ️  Найден администратор: {admin_users[0].username}")
                    print(f"🔑 API ключ администратора: {admin_users[0].api_key}")
                else:
                    print("⚠️  Администратор не найден! Создаем нового...")
                    import secrets
                    import string

                    def generate_secure_api_key(length=64):
                        alphabet = string.ascii_letters + string.digits + "_-"
                        return "".join(secrets.choice(alphabet) for _ in range(length))

                    admin_api_key = generate_secure_api_key()

                    admin_user = User(
                        username="admin",
                        email=settings.admin_email,
                        api_key=admin_api_key,
                        balance=0.0,
                        role="admin",
                        is_active=True,
                    )
                    session.add(admin_user)
                    await session.commit()
                    print("✅ Администратор создан")
                    print(f"🔑 API ключ администратора: {admin_api_key}")
                    print(
                        "⚠️  СОХРАНИТЕ ЭТОТ КЛЮЧ! Он понадобится для доступа к админ-панели"
                    )

            print("✅ Все начальные данные добавлены успешно!")
            return True

        except Exception as e:
            print(f"❌ Ошибка добавления начальных данных: {e}")
            await session.rollback()
            return False


async def verify_database():
    """Проверка корректности базы данных"""
    print("🔍 Проверка базы данных...")

    async with async_session_maker() as session:
        try:
            # Проверяем количество записей в основных таблицах
            tables_to_check = [
                (Country, "стран"),
                (Service, "сервисов"),
                (Price, "цен"),
                (Setting, "настроек"),
                (User, "пользователей"),
                (SMSProvider, "провайдеров"),
            ]

            for model, name in tables_to_check:
                result = await session.execute(select(model))
                count = len(result.scalars().all())
                print(f"📊 {name.capitalize()}: {count}")

            # Дополнительная проверка таблицы провайдеров через SQL
            try:
                result = await session.execute(
                    text("SELECT COUNT(*) FROM sms_providers")
                )
                provider_count = result.scalar()
                print(f"📊 Провайдеров (SQL): {provider_count}")
            except Exception as e:
                print(f"⚠️  Не удалось проверить таблицу провайдеров: {e}")

            print("✅ Проверка базы данных завершена")
            return True

        except Exception as e:
            print(f"❌ Ошибка проверки базы данных: {e}")
            return False


async def check_and_fix_schema_conflicts():
    """Проверяет и исправляет конфликты схемы БД"""
    print("🔍 Проверка возможных конфликтов схемы...")

    async with engine.begin() as conn:
        try:
            # Проверяем наличие потенциально проблемных колонок
            columns_to_check = [
                ("users", "last_login"),
                ("users", "updated_at"),
                ("users", "reserved_balance"),
            ]

            conflicts_found = False

            for table_name, column_name in columns_to_check:
                result = await conn.execute(
                    text(
                        """
                        SELECT column_name
                        FROM information_schema.columns
                        WHERE table_name = :table AND column_name = :column
                    """
                    ),
                    {"table": table_name, "column": column_name},
                )

                if result.fetchone():
                    print(f"ℹ️  Колонка {table_name}.{column_name} уже существует")
                    conflicts_found = True

            if conflicts_found:
                print(
                    "🔧 Обнаружены существующие колонки, синхронизируем историю миграций..."
                )
                # Помечаем все существующие миграции как применённые
                await sync_migration_history()
                return True
            else:
                print("✅ Конфликтов схемы не обнаружено")
                return True

        except Exception as e:
            print(f"⚠️  Ошибка проверки схемы: {e}")
            return True  # Продолжаем выполнение


async def sync_migration_history():
    """Синхронизирует историю миграций с текущим состоянием БД"""
    try:
        # Помечаем все миграции как выполненные
        result = subprocess.run(
            ["alembic", "stamp", "head"], capture_output=True, text=True, cwd="/app"
        )

        if result.returncode == 0:
            print("✅ История миграций синхронизирована")
        else:
            print(f"⚠️  Предупреждение при синхронизации: {result.stderr}")

    except Exception as e:
        print(f"⚠️  Ошибка синхронизации истории миграций: {e}")


async def main():
    """Основная функция инициализации"""
    print("🚀 Запуск полной инициализации базы данных...")

    # Шаг 1: Ожидание готовности БД
    if not await wait_for_database():
        print("❌ Не удалось дождаться готовности базы данных")
        sys.exit(1)

    # Шаг 2: Проверка и исправление конфликтов схемы
    await check_and_fix_schema_conflicts()

    # Шаг 3: Применение миграций
    if not await run_alembic_migrations():
        print("⚠️  Миграции не применены, пробуем создать таблицы напрямую")
        if not await create_tables_if_not_exist():
            print("❌ Не удалось создать таблицы")
            sys.exit(1)

    # Шаг 4: Добавление начальных данных
    if not await add_initial_data():
        print("❌ Не удалось добавить начальные данные")
        sys.exit(1)

    # Шаг 5: Проверка результата
    if not await verify_database():
        print("❌ Проверка базы данных не прошла")
        sys.exit(1)

    print("🎉 Инициализация базы данных завершена успешно!")


if __name__ == "__main__":
    asyncio.run(main())
