#!/bin/bash

# Скрипт для исправления SSL на удаленном сервере
# Подключается к серверу и устанавливает Cloudflare сертификаты

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Функции для вывода
error() { echo -e "${RED}❌ $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
step() { echo -e "${BLUE}🔄 $1${NC}"; }

# Конфигурация сервера (как в deploy.sh)
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PATH="/opt/sms_proxy"
SSH_KEY="$HOME/.ssh/sms_proxy_server"

# Функции для работы с сервером
ssh_exec() {
    local cmd="$1"
    if [ -n "$SSH_KEY" ] && [ -f "$SSH_KEY" ]; then
        ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "$cmd"
    else
        ssh "$SERVER_USER@$SERVER_IP" "$cmd"
    fi
}

scp_copy() {
    local src="$1"
    local dst="$2"
    if [ -n "$SSH_KEY" ] && [ -f "$SSH_KEY" ]; then
        scp -i "$SSH_KEY" "$src" "$SERVER_USER@$SERVER_IP:$dst"
    else
        scp "$src" "$SERVER_USER@$SERVER_IP:$dst"
    fi
}

# Проверка соединения с сервером
check_connection() {
    step "Проверка соединения с сервером $SERVER_IP..."

    if [ -n "$SSH_KEY" ] && [ -f "$SSH_KEY" ]; then
        info "Используется SSH ключ: $SSH_KEY"
    else
        warning "SSH ключ не найден, потребуется ввод пароля"
    fi

    if ssh_exec "echo 'OK'" &>/dev/null; then
        success "Соединение установлено"
    else
        error "Не удается подключиться к серверу $SERVER_IP"
        exit 1
    fi
}

echo "🔒 Исправление SSL на сервере"
echo "Сервер: $SERVER_IP"
echo "================================"

# Проверяем что в правильной папке
if [ ! -f "docker-compose.yml" ]; then
    error "Запустите из корневой папки проекта!"
    exit 1
fi

# Проверяем соединение
check_connection

# Создаем временную папку для сертификатов
TEMP_DIR=$(mktemp -d)
info "Временная папка: $TEMP_DIR"

echo ""
info "📋 ИНСТРУКЦИЯ:"
echo "1. Откройте https://dash.cloudflare.com"
echo "2. Выберите домен cock-liz.com"
echo "3. Перейдите SSL/TLS → Origin Server"
echo "4. Нажмите 'Create Certificate'"
echo "5. Выберите домены: cock-liz.com, *.cock-liz.com"
echo "6. Выберите срок 15 лет и нажмите Create"
echo ""

# Получаем сертификат
echo "🔑 Вставьте CERTIFICATE (с -----BEGIN и -----END):"
echo "После вставки нажмите Enter дважды:"
echo ""

cert_lines=""
while IFS= read -r line; do
    if [[ -z "$line" && -n "$cert_lines" ]]; then
        break
    fi
    cert_lines+="$line"$'\n'
done

echo "$cert_lines" > "$TEMP_DIR/cert.pem"
success "Сертификат сохранен локально"

echo ""
echo "🔐 Теперь вставьте PRIVATE KEY (с -----BEGIN и -----END):"
echo "После вставки нажмите Enter дважды:"
echo ""

key_lines=""
while IFS= read -r line; do
    if [[ -z "$line" && -n "$key_lines" ]]; then
        break
    fi
    key_lines+="$line"$'\n'
done

echo "$key_lines" > "$TEMP_DIR/key.pem"
success "Ключ сохранен локально"

# Проверяем сертификат локально
step "Проверяем сертификат..."
if openssl x509 -in "$TEMP_DIR/cert.pem" -noout -text > /dev/null 2>&1; then
    success "Сертификат валидный"
else
    error "Сертификат поврежден!"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Создаем backup на сервере
step "Создаем backup на сервере..."
ssh_exec "cd '$SERVER_PATH' && mkdir -p nginx/ssl/backup && \
          if [ -f nginx/ssl/cert.pem ]; then cp nginx/ssl/cert.pem nginx/ssl/backup/old_cert_\$(date +%Y%m%d_%H%M%S).pem; fi && \
          if [ -f nginx/ssl/key.pem ]; then cp nginx/ssl/key.pem nginx/ssl/backup/old_key_\$(date +%Y%m%d_%H%M%S).pem; fi"

# Отправляем сертификаты на сервер
step "Отправляем сертификаты на сервер..."
scp_copy "$TEMP_DIR/cert.pem" "$SERVER_PATH/nginx/ssl/cert.pem"
scp_copy "$TEMP_DIR/key.pem" "$SERVER_PATH/nginx/ssl/key.pem"

# Устанавливаем права доступа на сервере
step "Устанавливаем права доступа..."
ssh_exec "cd '$SERVER_PATH' && chmod 644 nginx/ssl/cert.pem && chmod 600 nginx/ssl/key.pem"

# Проверяем сертификат на сервере
step "Проверяем сертификат на сервере..."
if ssh_exec "cd '$SERVER_PATH' && openssl x509 -in nginx/ssl/cert.pem -noout -text > /dev/null 2>&1"; then
    success "Сертификат на сервере валидный"
else
    error "Проблема с сертификатом на сервере!"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Перезапускаем nginx на сервере
step "Перезапускаем nginx на сервере..."
ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server restart nginx"

# Проверяем статус
step "Проверяем статус сервисов..."
ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server ps"

# Тестируем HTTPS
step "Тестируем HTTPS соединение..."
sleep 5
if ssh_exec "curl -sk https://cock-liz.com/health | grep -q 'OK'"; then
    success "HTTPS работает!"
else
    warning "HTTPS может не работать, проверьте логи"
fi

# Удаляем временную папку
rm -rf "$TEMP_DIR"

echo ""
success "🎉 SSL сертификаты установлены на сервере!"
echo ""
info "Теперь:"
echo "1. Откройте Cloudflare панель"
echo "2. SSL/TLS → Overview → поставьте 'Full (strict)'"
echo "3. SSL/TLS → Edge Certificates → включите 'Always Use HTTPS'"
echo ""
success "Проверьте сайт: https://cock-liz.com"
