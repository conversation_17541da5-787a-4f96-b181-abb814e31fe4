#!/usr/bin/env python3
"""
Скрипт для загрузки данных из провайдеров
Загружает справочники стран, сервисов и цен из динамических провайдеров
"""
from __future__ import annotations

import asyncio
from decimal import Decimal
import os
import sys
from typing import Dict, List, Tuple

# Добавляем корневую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import engine
from app.core.logger import critical, debug, get_logger, info, warning
from app.models import Country, Price, Service
from app.providers.dynamic_manager import dynamic_manager
from app.services.mapping_service import mapping_service

logger = get_logger(__name__)


async def load_countries(session: AsyncSession) -> Dict[int, Country]:
    """Загрузка стран в базу данных"""
    info("Загрузка стран...")

    countries = {}

    # Получаем маппинги из сервиса
    country_mappings = mapping_service.get_all_countries()

    for sms_code, (firefox_code, calling_code, name) in country_mappings.items():
        # Проверяем, есть ли уже такая страна
        result = await session.execute(select(Country).where(Country.code == sms_code))
        country = result.scalar_one_or_none()

        if not country:
            country = Country(code=sms_code, name=name, is_active=True)
            session.add(country)
            info(f"Добавлена страна: {name} (код {sms_code})")
        else:
            info(f"Страна уже существует: {name} (код {sms_code})")

        countries[sms_code] = country

    await session.commit()
    info(f"Загружено {len(countries)} стран")
    return countries


async def load_services(session: AsyncSession) -> Dict[str, Service]:
    """Загрузка сервисов в базу данных"""
    info("Загрузка сервисов...")

    services = {}

    # Получаем маппинги из сервиса
    service_mappings = mapping_service.get_all_services()

    for sms_code, (firefox_iid, name) in service_mappings.items():
        # Проверяем, есть ли уже такой сервис
        result = await session.execute(select(Service).where(Service.code == sms_code))
        service = result.scalar_one_or_none()

        if not service:
            service = Service(code=sms_code, name=name, is_active=True)
            session.add(service)
            info(f"Добавлен сервис: {name} (код {sms_code})")
        else:
            info(f"Сервис уже существует: {name} (код {sms_code})")

        services[sms_code] = service

    await session.commit()
    info(f"Загружено {len(services)} сервисов")
    return services


async def load_firefox_prices(
    session: AsyncSession, countries: Dict[int, Country], services: Dict[str, Service]
):
    """Загрузка цен из Firefox API"""
    info("Загрузка цен из Firefox...")

    try:
        async with dynamic_manager.get_provider("Firefox") as firefox:
            # Получаем список цен от Firefox
            prices_data = await firefox.get_prices()

            if not prices_data.get("success"):
                critical("Не удалось получить цены от Firefox")
                return

            # Обрабатываем данные цен
            # Firefox возвращает JSON с ценами по странам и сервисам
            for item in prices_data.get("items", []):
                try:
                    # Парсим данные из Firefox
                    country_code = item.get("Country_ID")
                    service_iid = item.get("Item_ID")
                    price_value = float(item.get("Item_UPrice", 0))

                    # Находим соответствующие страну и сервис
                    country = None
                    service = None

                    # Ищем страну по коду Firefox
                    sms_country_code = mapping_service.get_sms_country_code(
                        str(country_code)
                    )
                    country = (
                        countries.get(sms_country_code) if sms_country_code else None
                    )

                    # Ищем сервис по IID Firefox
                    sms_service_code = mapping_service.get_sms_service_code(
                        str(service_iid)
                    )
                    service = (
                        services.get(sms_service_code) if sms_service_code else None
                    )

                    if country and service and price_value > 0:
                        # Проверяем, есть ли уже такая цена
                        result = await session.execute(
                            select(Price).where(
                                Price.country_id == country.id,
                                Price.service_id == service.id,
                            )
                        )
                        price = result.scalar_one_or_none()

                        if not price:
                            price = Price(
                                country_id=country.id,
                                service_id=service.id,
                                price=Decimal(str(price_value * 1.2)),  # Наценка 20%
                                provider_price_firefox=Decimal(str(price_value)),
                                available=100,  # Дефолтное значение
                            )
                            session.add(price)
                            info(
                                f"Добавлена цена Firefox: {country.name} - {service.name} = {price_value}"
                            )
                        else:
                            # Обновляем цену Firefox
                            price.provider_price_firefox = Decimal(str(price_value))
                            if not price.provider_price_smslive:
                                price.price = Decimal(str(price_value * 1.2))
                            info(
                                f"Обновлена цена Firefox: {country.name} - {service.name} = {price_value}"
                            )

                except Exception as e:
                    critical(f"Ошибка обработки цены Firefox: {e}")
                    continue

            await session.commit()
            info("Цены Firefox загружены")

    except Exception as e:
        critical(f"Ошибка загрузки цен Firefox: {e}")


async def load_smslive_prices(
    session: AsyncSession, countries: Dict[int, Country], services: Dict[str, Service]
):
    """Загрузка цен из SMSLive API"""
    info("Загрузка цен из SMSLive...")

    try:
        async with dynamic_manager.get_provider("SMSLive") as smslive:
            # Получаем список цен от SMSLive
            prices_data = await smslive.get_prices()

            if not prices_data:
                critical("Не удалось получить цены от SMSLive")
                return

            # SMSLive возвращает структуру: {"country_code": {"service_code": {"price": "count"}}}
            for country_code_str, services_data in prices_data.items():
                try:
                    country_code = int(country_code_str)
                    country = countries.get(country_code)

                    if not country:
                        continue

                    for service_code, price_info in services_data.items():
                        service = services.get(service_code)

                        if not service:
                            continue

                        # Берем минимальную цену из доступных
                        min_price = None
                        total_count = 0

                        for price_str, count_str in price_info.items():
                            try:
                                price_val = float(price_str)
                                count_val = int(count_str)

                                if min_price is None or price_val < min_price:
                                    min_price = price_val

                                total_count += count_val
                            except (ValueError, TypeError):
                                continue

                        if min_price and min_price > 0:
                            # Проверяем, есть ли уже такая цена
                            result = await session.execute(
                                select(Price).where(
                                    Price.country_id == country.id,
                                    Price.service_id == service.id,
                                )
                            )
                            price = result.scalar_one_or_none()

                            if not price:
                                price = Price(
                                    country_id=country.id,
                                    service_id=service.id,
                                    price=Decimal(str(min_price * 1.2)),  # Наценка 20%
                                    provider_price_smslive=Decimal(str(min_price)),
                                    available=total_count,
                                )
                                session.add(price)
                                info(
                                    f"Добавлена цена SMSLive: {country.name} - {service.name} = {min_price}"
                                )
                            else:
                                # Обновляем цену SMSLive
                                price.provider_price_smslive = Decimal(str(min_price))
                                price.available = total_count

                                # Пересчитываем итоговую цену как среднее + наценка
                                if price.provider_price_firefox:
                                    avg_price = (
                                        price.provider_price_firefox
                                        + Decimal(str(min_price))
                                    ) / 2
                                    price.price = avg_price * Decimal("1.2")
                                else:
                                    price.price = Decimal(str(min_price * 1.2))

                                info(
                                    f"Обновлена цена SMSLive: {country.name} - {service.name} = {min_price}"
                                )

                except Exception as e:
                    critical(
                        f"Ошибка обработки цены SMSLive для страны {country_code_str}: {e}"
                    )
                    continue

            await session.commit()
            info("Цены SMSLive загружены")

    except Exception as e:
        critical(f"Ошибка загрузки цен SMSLive: {e}")


async def main():
    """Основная функция загрузки данных"""
    info("Начинаем загрузку данных из провайдеров...")

    # SMS провайдеры теперь управляются через административную панель
    # Данные загружаются из таблицы sms_providers

    # Отладочная информация
    if settings:
        debug(f"Настройки системы загружены: debug_mode={settings.debug_mode}")
        debug(f"Уровень логирования: {settings.log_level}")
    else:
        warning("Настройки системы не найдены")

    async with AsyncSession(engine) as session:
        # Загружаем страны
        countries = await load_countries(session)

        # Загружаем сервисы
        services = await load_services(session)

        # Обновляем ID после коммита
        for country in countries.values():
            await session.refresh(country)

        for service in services.values():
            await session.refresh(service)

        # Загружаем цены из активных провайдеров
        try:
            # Получаем активные провайдеры из динамического менеджера
            active_providers = await dynamic_manager.get_active_providers()

            for provider in active_providers:
                provider_name = provider.name
                info(f"Загружаем цены из провайдера: {provider_name}")

                if provider_name.lower() == "firefox":
                    await load_firefox_prices(session, countries, services)
                elif provider_name.lower() == "smslive":
                    await load_smslive_prices(session, countries, services)
                else:
                    debug(f"Неизвестный провайдер для загрузки цен: {provider_name}")

        except Exception as e:
            critical(f"Ошибка при работе с динамическими провайдерами: {e}")
            warning("Пропускаем загрузку цен из провайдеров")

    info("Загрузка данных завершена!")


if __name__ == "__main__":
    asyncio.run(main())
