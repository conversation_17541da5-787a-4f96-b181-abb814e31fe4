#!/bin/bash

# Скрипт запуска для Docker контейнера SMS Proxy Service
# Выполняет инициализацию базы данных и запускает приложение

set -e

echo "🚀 Запуск SMS Proxy Service..."

# Функция для ожидания готовности PostgreSQL
wait_for_postgres() {
    echo "⏳ Ожидание готовности PostgreSQL..."

    until pg_isready -h postgres -p 5432 -U "${POSTGRES_USER:-sms_proxy_user}" -d "${POSTGRES_DB:-sms_proxy}"; do
        echo "PostgreSQL не готов - ожидание..."
        sleep 2
    done

    echo "✅ PostgreSQL готов к работе"
}

# Функция инициализации базы данных
init_database() {
    echo "🔄 Инициализация базы данных..."

    # Запускаем скрипт инициализации
    python /app/scripts/init_db_docker.py

    if [ $? -eq 0 ]; then
        echo "✅ База данных инициализирована успешно"
    else
        echo "❌ Ошибка инициализации базы данных"
        exit 1
    fi
}

# Функция инициализации статических файлов
init_static_files() {
    echo "🔄 Инициализация статических файлов..."

    # Проверяем, пуста ли директория статических файлов
    if [ ! "$(ls -A /app/static 2>/dev/null)" ]; then
        echo "📁 Директория /app/static пуста, копируем файлы..."

        # Копируем статические файлы из резервной копии
        if [ -d "/tmp/app_static_backup" ]; then
            cp -r /tmp/app_static_backup/* /app/static/ 2>/dev/null || true
            echo "✅ Статические файлы скопированы из резервной копии"
        else
            echo "⚠️  Резервная копия статических файлов не найдена, создаем структуру..."
            mkdir -p /app/static/admin/js /app/static/admin/css
            echo "📁 Структура директорий создана"
        fi

        # Устанавливаем правильные права доступа
        chown -R app:app /app/static
        chmod -R 755 /app/static

        echo "✅ Статические файлы инициализированы"
    else
        echo "✅ Статические файлы уже существуют"
    fi
}

# Функция загрузки данных провайдеров (опционально)
load_provider_data() {
    echo "🔄 Загрузка данных провайдеров..."

    # Проверяем, нужно ли загружать данные провайдеров
    if [ "${LOAD_PROVIDER_DATA:-false}" = "true" ]; then
        python /app/scripts/load_provider_data.py

        if [ $? -eq 0 ]; then
            echo "✅ Данные провайдеров загружены"
        else
            echo "⚠️  Ошибка загрузки данных провайдеров (не критично)"
        fi
    else
        echo "ℹ️  Загрузка данных провайдеров пропущена"
    fi
}

# Функция проверки здоровья приложения
health_check() {
    echo "🔍 Проверка готовности приложения..."

    # Даем приложению время на запуск
    sleep 5

    # Проверяем health endpoint
    for i in {1..10}; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            echo "✅ Приложение готово к работе"
            return 0
        fi
        echo "Попытка $i/10: приложение не готово..."
        sleep 3
    done

    echo "⚠️  Приложение может быть не готово, но продолжаем работу"
    return 0
}

# Основная логика запуска
main() {
    echo "📋 Параметры запуска:"
    echo "   - DATABASE_URL: ${DATABASE_URL}"
    echo "   - DEBUG: ${DEBUG:-false}"
    echo "   - LOG_LEVEL: ${LOG_LEVEL:-INFO}"
    echo "   - DEFAULT_PROVIDER: ${DEFAULT_PROVIDER:-both}"
    echo ""

    # Шаг 1: Ожидание PostgreSQL
    wait_for_postgres

    # Шаг 2: Инициализация базы данных
    init_database

    # Шаг 3: Инициализация статических файлов
    init_static_files

    # Шаг 4: Загрузка данных провайдеров (опционально)
    load_provider_data

    echo "🎯 Запуск основного приложения..."

    # Определяем количество воркеров в зависимости от режима
    if [ "${DEBUG:-false}" = "true" ]; then
        WORKERS=1
        RELOAD="--reload"
        echo "🐛 Режим отладки: 1 воркер с автоперезагрузкой"
    else
        WORKERS=${WORKERS:-4}
        RELOAD=""
        echo "🚀 Продакшн режим: $WORKERS воркеров"
    fi

    # Запускаем приложение в фоне для проверки здоровья
    uvicorn main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers $WORKERS \
        $RELOAD &

    APP_PID=$!

    # Проверяем здоровье приложения
    health_check

    # Ожидаем завершения приложения
    echo "✅ SMS Proxy Service запущен успешно (PID: $APP_PID)"
    echo "📡 API доступен по адресу: http://0.0.0.0:8000"
    echo "📚 Документация: http://0.0.0.0:8000/docs"
    echo "🛠️  Админ-панель: http://0.0.0.0:8000/admin"
    echo ""
    echo "🔄 Ожидание завершения приложения..."

    wait $APP_PID
}

# Обработка сигналов для корректного завершения
trap 'echo "🛑 Получен сигнал завершения, останавливаем приложение..."; kill $APP_PID 2>/dev/null; exit 0' SIGTERM SIGINT

# Запуск основной функции
main "$@"
