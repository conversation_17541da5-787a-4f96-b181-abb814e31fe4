#!/bin/bash

# Универсальный скрипт развертывания SMS Proxy Service
# Поддерживает локальное и удаленное развертывание с инкрементальными обновлениями

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для цветного вывода
error() { echo -e "${RED}❌ $1${NC}"; }
success() { echo -e "${GREEN}✅ $1${NC}"; }
warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
step() { echo -e "${BLUE}🔄 $1${NC}"; }

# Переменные
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
ENV_FILE="$PROJECT_DIR/.env.server"
COMPOSE_FILE="$PROJECT_DIR/docker-compose.yml"

# Конфигурация удаленного сервера
REMOTE_MODE="false"
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PATH="/opt/sms_proxy"
SSH_KEY="$HOME/.ssh/sms_proxy_server"

# Функция показа помощи
show_help() {
    echo "SMS Proxy Service - Универсальный скрипт развертывания"
    echo ""
    echo "Использование: $0 [команда] [опции]"
    echo ""
    echo "Команды:"
    echo "  deploy      - Полное развертывание"
    echo "  update      - Быстрое обновление без пересоздания БД"
    echo "  migrate     - Выполнение миграций БД"
    echo "  create-admin- Создание администратора"
    echo "  start       - Запуск сервисов"
    echo "  stop        - Остановка сервисов"
    echo "  restart     - Перезапуск сервисов"
    echo "  status      - Статус сервисов"
    echo "  logs        - Просмотр логов"
    echo "  backup      - Создание резервной копии БД"
    echo "  rollback    - Откат к предыдущей версии"
    echo "  diagnose    - Диагностика сервера"
    echo "  cleanup     - Очистка системы"
    echo "  deploy-domain - Развертывание конфигурации домена cock-liz.com"
    echo "  check-domain  - Проверка статуса домена cock-liz.com"
    echo ""
    echo "Опции:"
    echo "  --remote    - Выполнить на удаленном сервере"
    echo "  --force     - Принудительное выполнение"
    echo "  --debug     - Режим отладки"
    echo ""
    echo "Примеры:"
    echo "  $0 deploy               # Локальное развертывание"
    echo "  $0 deploy --remote      # Развертывание на сервере"
    echo "  $0 update --remote      # Быстрое обновление на сервере"
    echo "  $0 create-admin --remote# Создание администратора на сервере"
    echo "  $0 status --remote      # Статус сервисов на сервере"
    echo "  $0 logs api --remote    # Логи API с сервера"
    echo "  $0 deploy-domain --remote # Развертывание домена на сервере"
}

# Функции для работы с удаленным сервером
ssh_exec() {
    local cmd="$1"
    if [ -n "$SSH_KEY" ]; then
        ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "$cmd"
    else
        ssh "$SERVER_USER@$SERVER_IP" "$cmd"
    fi
}

scp_copy() {
    local src="$1"
    local dst="$2"
    if [ -n "$SSH_KEY" ]; then
        scp -i "$SSH_KEY" -r "$src" "$SERVER_USER@$SERVER_IP:$dst"
    else
        scp -r "$src" "$SERVER_USER@$SERVER_IP:$dst"
    fi
}

# Проверка соединения с сервером
check_remote_connection() {
    if [ "$REMOTE_MODE" != "true" ]; then
        return 0
    fi

    step "Проверка соединения с сервером $SERVER_IP..."

    # Проверяем SSH ключ
    if [ -n "$SSH_KEY" ]; then
        if [ -f "$SSH_KEY" ]; then
            info "Используется SSH ключ: $SSH_KEY"
        else
            warning "SSH ключ не найден: $SSH_KEY"
            warning "Потребуется ввод пароля"
        fi
    else
        warning "SSH ключ не задан, потребуется ввод пароля"
    fi

    if ssh_exec "echo 'OK'" &>/dev/null; then
        success "Соединение установлено"
    else
        error "Не удается подключиться к серверу $SERVER_IP"
        if [ -n "$SSH_KEY" ] && [ -f "$SSH_KEY" ]; then
            error "Возможно, SSH ключ не добавлен на сервер"
            info "Попробуйте: ssh-copy-id -i $SSH_KEY.pub $SERVER_USER@$SERVER_IP"
        fi
        exit 1
    fi
}

# Выполнение команды (локально или удаленно)
execute_cmd() {
    local cmd="$1"

    if [ "$REMOTE_MODE" = "true" ]; then
        ssh_exec "$cmd"
    else
        eval "$cmd"
    fi
}

# Проверка зависимостей
check_dependencies() {
    step "Проверка зависимостей..."

    if [ "$REMOTE_MODE" = "true" ]; then
        check_remote_connection
        ssh_exec "command -v docker >/dev/null && command -v docker-compose >/dev/null" || {
            error "Docker не установлен на сервере"
            exit 1
        }

        # Проверяем rsync для корректной синхронизации
        if ! command -v rsync &> /dev/null; then
            warning "rsync не найден, используем альтернативный метод копирования"
        fi
    else
        for dep in docker docker-compose; do
            if ! command -v "$dep" &> /dev/null; then
                error "Отсутствует зависимость: $dep"
                exit 1
            fi
        done
    fi

    success "Зависимости проверены"
}

# Проверка установки проекта
check_installation() {
    local project_path="$PROJECT_DIR"

    if [ "$REMOTE_MODE" = "true" ]; then
        project_path="$SERVER_PATH"
        if ssh_exec "[ -d '$project_path' ]"; then
            return 0
        else
            return 1
        fi
    else
        if [ -d "$project_path" ]; then
            return 0
        else
            return 1
        fi
    fi
}

# Копирование проекта на сервер
sync_to_remote() {
    if [ "$REMOTE_MODE" != "true" ]; then
        return 0
    fi

    step "Синхронизация с сервером..."

    # Создаем директорию на сервере
    ssh_exec "mkdir -p '$SERVER_PATH'"

    # Создаем временную директорию для исключения ненужных файлов
    local temp_dir=$(mktemp -d)
    rsync -av --exclude='__pycache__' --exclude='*.pyc' --exclude='*.pyo' \
          --exclude='.git' --exclude='*.log' --exclude='logs/' \
          "$PROJECT_DIR/" "$temp_dir/"

    # Копируем очищенный проект
    scp_copy "$temp_dir/." "$SERVER_PATH/"

    # Удаляем временную директорию
    rm -rf "$temp_dir"

    # Убеждаемся что nginx конфигурация скопирована
    if [ -d "$PROJECT_DIR/nginx" ]; then
        scp_copy "$PROJECT_DIR/nginx/" "$SERVER_PATH/nginx/"
        info "Конфигурация Nginx скопирована"
    fi

    # Убеждаемся что миграции Alembic скопированы
    if [ -d "$PROJECT_DIR/alembic" ]; then
        scp_copy "$PROJECT_DIR/alembic/" "$SERVER_PATH/alembic/"
        info "Миграции Alembic скопированы"
    fi

    # Создаем необходимые директории и устанавливаем права на сервере
    ssh_exec "
        cd '$SERVER_PATH' && \
        mkdir -p logs data nginx/ssl && \
        chmod -R 755 logs data && \
        chmod +x scripts/deploy.sh scripts/docker-entrypoint.sh 2>/dev/null || true
    "

    # Создаем SSL сертификаты на сервере если их нет
    ssh_exec "
        cd '$SERVER_PATH' && \
        if [ ! -f nginx/ssl/cert.pem ]; then \
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout nginx/ssl/key.pem \
                -out nginx/ssl/cert.pem \
                -subj '/C=RU/ST=Moscow/L=Moscow/O=SMS Proxy/CN=localhost' \
                2>/dev/null || echo 'Предупреждение: не удалось создать SSL сертификаты'; \
        fi
    "

    success "Проект синхронизирован с сервером"
}

# Создание резервной копии
create_backup() {
    step "Создание резервной копии..."

    local backup_cmd
    local path="$PROJECT_DIR"

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    backup_cmd="cd '$path' && if [ -d app ]; then cp -r app app_backup_\$(date +%Y%m%d_%H%M%S); fi"

    execute_cmd "$backup_cmd"

    success "Резервная копия создана"
}

# Выполнение миграций БД
run_migrations() {
    step "Выполнение миграций базы данных..."

    local path="$PROJECT_DIR"
    local env_flag=""

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
        env_flag="--env-file $path/.env"
    else
        env_flag="--env-file $ENV_FILE"
        env_flag="--env-file $path/.env"
    fi

    # Останавливаем и удаляем существующие контейнеры
    execute_cmd "cd '$path' && docker-compose $env_flag stop postgres api 2>/dev/null || true"
    execute_cmd "cd '$path' && docker-compose $env_flag rm -f postgres api 2>/dev/null || true"

    # Запускаем только postgres
    execute_cmd "cd '$path' && docker-compose $env_flag up -d postgres"

    # Ждем готовности БД
    step "Ожидание готовности базы данных..."
    sleep 15

    # Выполняем полную инициализацию БД через обновленный скрипт
    execute_cmd "cd '$path' && docker-compose $env_flag run --rm api python scripts/init_db_docker.py"

    # Проверяем что все необходимые таблицы созданы включая sms_providers
    step "Проверка созданных таблиц..."
    local check_tables_cmd="cd '$path' && docker-compose $env_flag exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -c \"
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
  AND table_type = 'BASE TABLE'
ORDER BY table_name;
\""

    execute_cmd "$check_tables_cmd"

    # Проверяем наличие критичных таблиц
    local verify_tables_cmd="cd '$path' && docker-compose $env_flag exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -tc \"
SELECT
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') THEN '✅ users' ELSE '❌ users отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'countries') THEN '✅ countries' ELSE '❌ countries отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'services') THEN '✅ services' ELSE '❌ services отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'prices') THEN '✅ prices' ELSE '❌ prices отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'activations') THEN '✅ activations' ELSE '❌ activations отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'sms_providers') THEN '✅ sms_providers' ELSE '❌ sms_providers отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN '✅ transactions' ELSE '❌ transactions отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'logs') THEN '✅ logs' ELSE '❌ logs отсутствует' END,
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'settings') THEN '✅ settings' ELSE '❌ settings отсутствует' END;
\""

    execute_cmd "$verify_tables_cmd"

    # Проверяем количество записей в таблице провайдеров
    step "Проверка данных в таблице провайдеров..."
    local check_providers_cmd="cd '$path' && docker-compose $env_flag exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -tc \"
SELECT
  'Провайдеров в БД: ' || COUNT(*) || ', активных: ' || COUNT(*) FILTER (WHERE is_active = true)
FROM sms_providers;
\""

    if execute_cmd "$check_providers_cmd" 2>/dev/null; then
        success "Таблица провайдеров работает корректно"
    else
        warning "Таблица провайдеров отсутствует, создаем принудительно..."
        local create_providers_table_cmd="cd '$path' && docker-compose $env_flag exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -c \"
CREATE TABLE IF NOT EXISTS sms_providers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    api_url VARCHAR(500) NOT NULL,
    api_key VARCHAR(500),
    username VARCHAR(100),
    password VARCHAR(500),
    provider_type VARCHAR(30) NOT NULL,
    api_format VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 100,
    max_requests_per_minute INTEGER DEFAULT 60,
    timeout_seconds INTEGER DEFAULT 30,
    settings JSONB DEFAULT '{}',
    last_test_at TIMESTAMP,
    last_test_result VARCHAR(20),
    test_error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by_user_id INTEGER REFERENCES users(id)
);\""
        execute_cmd "$create_providers_table_cmd"
        success "Таблица провайдеров создана принудительно"
    fi

    success "Миграции базы данных завершены"
}

# Полное развертывание
full_deploy() {
    echo "🚀 SMS Proxy Service - Полное развертывание"
    if [ "$REMOTE_MODE" = "true" ]; then
        echo "Сервер: $SERVER_IP"
    else
        echo "Локальное развертывание"
    fi
    echo "============================================"

    check_dependencies

    if [ "$REMOTE_MODE" = "true" ]; then
        sync_to_remote

        # Выполняем развертывание на сервере
        ssh_exec "cd '$SERVER_PATH' && chmod +x scripts/deploy.sh && ./scripts/deploy.sh deploy"
    else
        # Локальное развертывание
        if [ ! -f "$ENV_FILE" ]; then
            if [ -f "$PROJECT_DIR/.env.example" ]; then
                cp "$PROJECT_DIR/.env.example" "$ENV_FILE"
                warning "Отредактируйте файл $ENV_FILE перед продолжением"
                exit 1
            fi
        fi

        # Создаем директории с правильными правами доступа
        mkdir -p logs data nginx/ssl

        # Устанавливаем права доступа для Docker контейнера
        # UID/GID 1000 соответствует пользователю app в контейнере
        sudo chown -R 1000:1000 logs data 2>/dev/null || {
            # Если sudo недоступен, используем альтернативный подход
            chmod -R 777 logs data
            warning "Установлены широкие права доступа. Рекомендуется настроить корректные права."
        }

        # Создаем SSL сертификаты
        if [ ! -f "nginx/ssl/cert.pem" ]; then
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout "nginx/ssl/key.pem" \
                -out "nginx/ssl/cert.pem" \
                -subj "/C=RU/ST=Moscow/L=Moscow/O=SMS Proxy/CN=localhost" \
                2>/dev/null
        fi

        cd "$PROJECT_DIR"
        export $(grep -v '^#' "$ENV_FILE" | xargs)
        docker-compose --env-file "$ENV_FILE" build
        docker-compose --env-file "$ENV_FILE" up -d
    fi

    success "Развертывание завершено"
    check_status
}

# Быстрое обновление
quick_update() {
    echo "🔄 SMS Proxy Service - Быстрое обновление"
    if [ "$REMOTE_MODE" = "true" ]; then
        echo "Сервер: $SERVER_IP"
    fi
    echo "============================================"

    check_dependencies

    if ! check_installation; then
        warning "Проект не установлен. Выполняем полное развертывание..."
        full_deploy
        return
    fi

    create_backup

    if [ "$REMOTE_MODE" = "true" ]; then
        # Обновление на сервере
        step "Обновление кода на сервере..."

        # Создаем временную директорию для чистого кода
        local temp_dir=$(mktemp -d)
        rsync -av --exclude='__pycache__' --exclude='*.pyc' --exclude='*.pyo' \
              --exclude='.git' --exclude='*.log' --exclude='logs/' \
              "$PROJECT_DIR/app/" "$temp_dir/"

        # Копируем только приложение
        scp_copy "$temp_dir/." "$SERVER_PATH/app/"
        rm -rf "$temp_dir"

        # Копируем основные файлы
        if [ -f "$PROJECT_DIR/main.py" ]; then
            scp_copy "$PROJECT_DIR/main.py" "$SERVER_PATH/"
        fi

        if [ -f "$PROJECT_DIR/requirements.txt" ]; then
            scp_copy "$PROJECT_DIR/requirements.txt" "$SERVER_PATH/"
        fi

        # Копируем конфигурационные файлы
        if [ -f "$ENV_FILE" ]; then
            scp_copy "$ENV_FILE" "$SERVER_PATH/.env.server"
        fi

        if [ -f "$PROJECT_DIR/docker-compose.yml" ]; then
            scp_copy "$PROJECT_DIR/docker-compose.yml" "$SERVER_PATH/"
        fi

        # Копируем обновленный скрипт развертывания
        scp_copy "$PROJECT_DIR/scripts/deploy.sh" "$SERVER_PATH/scripts/"

        # Копируем миграции Alembic (важно для обновлений БД)
        if [ -d "$PROJECT_DIR/alembic" ]; then
            scp_copy "$PROJECT_DIR/alembic/" "$SERVER_PATH/alembic/"
            info "Миграции Alembic обновлены"
        fi

        # Копируем Dockerfile для пересборки образа
        if [ -f "$PROJECT_DIR/Dockerfile" ]; then
            scp_copy "$PROJECT_DIR/Dockerfile" "$SERVER_PATH/"
        fi

        # Обеспечиваем правильные права доступа
        ssh_exec "
            cd '$SERVER_PATH' && \
            mkdir -p logs data && \
            chmod -R 755 logs data && \
            chmod +x scripts/deploy.sh scripts/docker-entrypoint.sh 2>/dev/null || true && \
            docker-compose --env-file .env.server down && \
            docker-compose --env-file .env.server build api
        "

        # Выполняем полную инициализацию БД через обновленный скрипт
        ssh_exec "
            cd '$SERVER_PATH' && \
            docker-compose --env-file .env.server up -d postgres && \
            sleep 15 && \
            docker-compose --env-file .env.server run --rm api python scripts/init_db_docker.py
        "

        # Проверяем что все необходимые таблицы созданы включая sms_providers
        ssh_exec "
            cd '$SERVER_PATH' && \
            echo 'ℹ️  Проверка созданных таблиц...' && \
            docker-compose --env-file .env.server exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -tc \"
SELECT
  'Таблиц в БД: ' || COUNT(*) || ', включая: ' || STRING_AGG(table_name, ', ' ORDER BY table_name)
FROM information_schema.tables
WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
\" && \
            echo 'ℹ️  Проверка таблицы провайдеров...' && \
            docker-compose --env-file .env.server exec postgres psql -U \${POSTGRES_USER:-sms_proxy_user} -d \${POSTGRES_DB:-sms_proxy} -tc \"
SELECT
  CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'sms_providers')
    THEN '✅ Таблица sms_providers создана успешно'
    ELSE '❌ Таблица sms_providers отсутствует!'
  END;
\" || echo '⚠️  Ошибка проверки таблицы провайдеров'
        "

        # Запускаем все сервисы
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server up -d"

    else
        # Локальное обновление
        step "Остановка API сервиса..."
        cd "$PROJECT_DIR"
        docker-compose --env-file "$ENV_FILE" stop api
        docker-compose --env-file "$ENV_FILE" rm -f api

        step "Пересборка образа..."
        docker-compose --env-file "$ENV_FILE" build api

        step "Выполнение миграций..."
        run_migrations

        step "Запуск обновленного сервиса..."
        docker-compose --env-file "$ENV_FILE" up -d
    fi

    success "Обновление завершено"
    check_status
}

# Проверка статуса
check_status() {
    step "Проверка статуса сервисов..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    execute_cmd "cd '$path' && docker-compose ps"

    # Проверка API
    sleep 5
    if [ "$REMOTE_MODE" = "true" ]; then
        ssh_exec "curl -f http://localhost:8000/health" &>/dev/null && \
            success "API доступен" || warning "API не отвечает"
    else
        curl -f http://localhost:8000/health &>/dev/null && \
            success "API доступен: http://localhost:8000" || warning "API не отвечает"
    fi
}

# Запуск сервисов
start_services() {
    step "Запуск сервисов..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    execute_cmd "cd '$path' && docker-compose up -d"

    success "Сервисы запущены"
    check_status
}

# Остановка сервисов
stop_services() {
    step "Остановка сервисов..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    execute_cmd "cd '$path' && docker-compose down"

    success "Сервисы остановлены"
}

# Перезапуск сервисов
restart_services() {
    step "Перезапуск сервисов..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    execute_cmd "cd '$path' && docker-compose restart"

    success "Сервисы перезапущены"
    check_status
}

# Просмотр логов
show_logs() {
    local service="${1:-}"
    local path="$PROJECT_DIR"
    local env_flag=""

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
        env_flag="--env-file $path/.env.server"
    else
        env_flag="--env-file $ENV_FILE"
    fi

    # Проверяем состояние Docker на сервере
    if [ "$REMOTE_MODE" = "true" ]; then
        step "Проверка состояния Docker на сервере..."
        ssh_exec "cd '$path' && docker-compose $env_flag ps" || {
            error "Не удается получить статус контейнеров"
            return 1
        }
    fi

    if [ -n "$service" ]; then
        step "Логи сервиса $service..."
        execute_cmd "cd '$path' && docker-compose $env_flag logs --tail=100 '$service'"
    else
        step "Логи всех сервисов..."
        execute_cmd "cd '$path' && docker-compose $env_flag logs --tail=100"
    fi
}

# Создание резервной копии БД
backup_database() {
    step "Создание резервной копии базы данных..."

    local path="$PROJECT_DIR"
    local backup_dir="$path/backups"
    local backup_file="sms_proxy_$(date +%Y%m%d_%H%M%S).sql"

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
        backup_dir="$path/backups"
        ssh_exec "mkdir -p '$backup_dir'"
        ssh_exec "cd '$path' && docker-compose exec -T postgres pg_dump -U sms_proxy_user -d sms_proxy > '$backup_dir/$backup_file'"
    else
        mkdir -p "$backup_dir"
        cd "$PROJECT_DIR"
        export $(grep -v '^#' "$ENV_FILE" | xargs)
        docker-compose exec -T postgres pg_dump -U "${POSTGRES_USER:-sms_proxy_user}" -d "${POSTGRES_DB:-sms_proxy}" > "$backup_dir/$backup_file"
    fi

    success "Резервная копия создана: $backup_file"
}

# Откат к предыдущей версии
rollback() {
    step "Откат к предыдущей версии..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    local backup_cmd="cd '$path' && ls -1d app_backup_* 2>/dev/null | tail -1"
    local backup_dir

    if [ "$REMOTE_MODE" = "true" ]; then
        backup_dir=$(ssh_exec "$backup_cmd" || echo "")
    else
        backup_dir=$(eval "$backup_cmd" || echo "")
    fi

    if [ -z "$backup_dir" ]; then
        error "Резервная копия не найдена"
        exit 1
    fi

    info "Восстановление из: $backup_dir"

    execute_cmd "
        cd '$path' && \
        docker-compose stop api && \
        rm -rf app && \
        mv '$backup_dir' app && \
        docker-compose up -d
    "

    success "Откат выполнен"
    check_status
}

# Создание администратора
create_admin() {
    step "Создание администратора..."

    local path="$PROJECT_DIR"
    local env_flag=""

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
        env_flag="--env-file $path/.env.server"
    else
        env_flag="--env-file $ENV_FILE"
    fi

    # Запускаем скрипт создания администратора через Docker
    if [ "$REMOTE_MODE" = "true" ]; then
        execute_cmd "cd '$path' && docker-compose $env_flag run --rm -e PYTHONPATH=/app api python -c \"
import asyncio
import sys
import secrets
import string
sys.path.insert(0, '/app')

from app.models import User
from app.core.database import async_session_maker
from app.core.config import settings
from sqlalchemy import select

async def create_admin():
    async with async_session_maker() as session:
        try:
            # Проверяем, есть ли уже администратор
            result = await session.execute(select(User).where(User.role == 'admin'))
            existing_admin = result.scalar_one_or_none()

            if existing_admin:
                print(f'ℹ️  Администратор уже существует: {existing_admin.username}')
                print(f'🔑 API ключ: {existing_admin.api_key}')
                return

            # Генерируем сложный API ключ
            def generate_secure_api_key(length=64):
                alphabet = string.ascii_letters + string.digits + '_-'
                return ''.join(secrets.choice(alphabet) for _ in range(length))

            admin_api_key = generate_secure_api_key()

            # Создаем администратора
            admin_user = User(
                username='admin',
                email=settings.admin_email or '<EMAIL>',
                api_key=admin_api_key,
                balance=0.0,
                role='admin',
                is_active=True
            )

            session.add(admin_user)
            await session.commit()

            print('✅ Администратор создан успешно!')
            print(f'👤 Пользователь: {admin_user.username}')
            print(f'📧 Email: {admin_user.email}')
            print(f'🔑 API ключ: {admin_api_key}')
            print('⚠️  СОХРАНИТЕ ЭТОТ КЛЮЧ! Он понадобится для доступа к админ-панели')

        except Exception as e:
            print(f'❌ Ошибка создания администратора: {e}')
            await session.rollback()

asyncio.run(create_admin())
\""
    else
        execute_cmd "cd '$path' && docker-compose $env_flag run --rm -e PYTHONPATH=/app api python -c \"
import asyncio
import sys
import secrets
import string
sys.path.insert(0, '/app')

from app.models import User
from app.core.database import async_session_maker
from app.core.config import settings
from sqlalchemy import select

async def create_admin():
    async with async_session_maker() as session:
        try:
            # Проверяем, есть ли уже администратор
            result = await session.execute(select(User).where(User.role == 'admin'))
            existing_admin = result.scalar_one_or_none()

            if existing_admin:
                print(f'ℹ️  Администратор уже существует: {existing_admin.username}')
                print(f'🔑 API ключ: {existing_admin.api_key}')
                return

            # Генерируем сложный API ключ
            def generate_secure_api_key(length=64):
                alphabet = string.ascii_letters + string.digits + '_-'
                return ''.join(secrets.choice(alphabet) for _ in range(length))

            admin_api_key = generate_secure_api_key()

            # Создаем администратора
            admin_user = User(
                username='admin',
                email=settings.admin_email or '<EMAIL>',
                api_key=admin_api_key,
                balance=0.0,
                role='admin',
                is_active=True
            )

            session.add(admin_user)
            await session.commit()

            print('✅ Администратор создан успешно!')
            print(f'👤 Пользователь: {admin_user.username}')
            print(f'📧 Email: {admin_user.email}')
            print(f'🔑 API ключ: {admin_api_key}')
            print('⚠️  СОХРАНИТЕ ЭТОТ КЛЮЧ! Он понадобится для доступа к админ-панели')

        except Exception as e:
            print(f'❌ Ошибка создания администратора: {e}')
            await session.rollback()

asyncio.run(create_admin())
\""
    fi

    success "Создание администратора завершено"
}

# Диагностика сервера
diagnose_server() {
    step "Диагностика состояния сервера..."

    if [ "$REMOTE_MODE" != "true" ]; then
        warning "Диагностика доступна только для удаленного режима"
        return 1
    fi

    info "=== Проверка системы ==="
    ssh_exec "uname -a && uptime"

    info "=== Проверка Docker ==="
    ssh_exec "docker --version && docker-compose --version" || {
        error "Docker не установлен или недоступен"
        return 1
    }

    info "=== Проверка проекта ==="
    ssh_exec "ls -la '$SERVER_PATH'" || {
        error "Проект не найден в $SERVER_PATH"
        return 1
    }

    info "=== Проверка конфигурации ==="
    ssh_exec "cd '$SERVER_PATH' && [ -f .env.server ] && echo 'Конфиг найден' || echo 'Конфиг отсутствует'"

    info "=== Состояние контейнеров ==="
    ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server ps"

    info "=== Проверка портов ==="
    ssh_exec "netstat -tlnp | grep -E ':(8000|5432|80|443)'"

    info "=== Последние логи Docker ==="
    ssh_exec "journalctl -u docker --since '1 hour ago' --no-pager -n 20" || true

    success "Диагностика завершена"
}

# Очистка
cleanup() {
    step "Очистка системы..."

    local path="$PROJECT_DIR"
    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
    fi

    # Останавливаем и удаляем все контейнеры проекта
    info "Останавливаем и удаляем контейнеры..."
    execute_cmd "cd '$path' && docker-compose down -v --remove-orphans"

    # Удаляем все контейнеры связанные с проектом (включая остановленные)
    info "Удаляем все контейнеры проекта..."
    execute_cmd "docker ps -a --filter 'name=sms_proxy' --format '{{.ID}}' | xargs -r docker rm -f"
    execute_cmd "docker ps -a --filter 'name=firefox_smslive' --format '{{.ID}}' | xargs -r docker rm -f"

    # Удаляем образы проекта
    info "Удаляем образы проекта..."
    execute_cmd "docker images --filter 'reference=*sms_proxy*' --format '{{.ID}}' | xargs -r docker rmi -f"
    execute_cmd "docker images --filter 'reference=*firefox_smslive*' --format '{{.ID}}' | xargs -r docker rmi -f"

    # Удаляем volumes с данными БД
    info "Удаляем volumes баз данных..."
    execute_cmd "docker volume ls --filter 'name=sms_proxy' --format '{{.Name}}' | xargs -r docker volume rm"
    execute_cmd "docker volume ls --filter 'name=firefox_smslive' --format '{{.Name}}' | xargs -r docker volume rm"

    # Очищаем неиспользуемые ресурсы Docker
    info "Очищаем неиспользуемые ресурсы Docker..."
    if [ "$FORCE_MODE" = "true" ]; then
        execute_cmd "docker system prune -af"
    else
        execute_cmd "docker system prune -f"
    fi

    # Удаляем сети проекта
    info "Удаляем сети проекта..."
    execute_cmd "docker network ls --filter 'name=sms_proxy' --format '{{.ID}}' | xargs -r docker network rm"
    execute_cmd "docker network ls --filter 'name=firefox_smslive' --format '{{.Name}}' | xargs -r docker network rm"

    success "Очистка завершена"
}

# Развертывание конфигурации домена cock-liz.com
deploy_domain() {
    echo "🌐 Развертывание конфигурации домена cock-liz.com"
    if [ "$REMOTE_MODE" = "true" ]; then
        echo "Сервер: $SERVER_IP"
    else
        echo "Локальное развертывание"
    fi
    echo "============================================"

    check_dependencies

    # Проверяем наличие необходимых файлов домена
    step "Проверка файлов конфигурации домена..."

    local domain_files=(
        "nginx/nginx.conf"
        "nginx/ssl/cert.pem"
        "nginx/ssl/key.pem"
        ".env"
        "docker-compose.yml"
    )

    for file in "${domain_files[@]}"; do
        if [ ! -f "$PROJECT_DIR/$file" ]; then
            error "Отсутствует файл: $file"
            exit 1
        fi
    done

    success "Все файлы домена найдены"

    if [ "$REMOTE_MODE" = "true" ]; then
        # Развертывание на удаленном сервере
        step "Создание резервной копии текущей конфигурации на сервере..."

        # Создаем резервную копию текущей конфигурации
        ssh_exec "cd '$SERVER_PATH' && mkdir -p backups/domain_config_\$(date +%Y%m%d_%H%M%S) && \
                  cp -r nginx/ backups/domain_config_\$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true && \
                  cp .env.server backups/domain_config_\$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true && \
                  cp docker-compose.yml backups/domain_config_\$(date +%Y%m%d_%H%M%S)/ 2>/dev/null || true"

        step "Копирование конфигурации nginx для домена cock-liz.com..."

        # Создаем директории на сервере
        ssh_exec "cd '$SERVER_PATH' && mkdir -p nginx/ssl logs/nginx"

        # Копируем конфигурацию nginx
        scp_copy "$PROJECT_DIR/nginx/nginx.conf" "$SERVER_PATH/nginx/"

        # Копируем SSL сертификаты
        step "Копирование SSL сертификатов для cock-liz.com..."
        scp_copy "$PROJECT_DIR/nginx/ssl/cert.pem" "$SERVER_PATH/nginx/ssl/"
        scp_copy "$PROJECT_DIR/nginx/ssl/key.pem" "$SERVER_PATH/nginx/ssl/"

        # Копируем обновленный .env файл
        step "Обновление переменных окружения для домена..."
        scp_copy "$PROJECT_DIR/.env" "$SERVER_PATH/.env.server"

        # Копируем обновленный docker-compose.yml
        step "Обновление конфигурации Docker Compose..."
        scp_copy "$PROJECT_DIR/docker-compose.yml" "$SERVER_PATH/"

        # Устанавливаем правильные права доступа
        step "Настройка прав доступа..."
        ssh_exec "cd '$SERVER_PATH' && \
                  chmod 644 nginx/nginx.conf && \
                  chmod 600 nginx/ssl/key.pem && \
                  chmod 644 nginx/ssl/cert.pem && \
                  chmod 644 .env.server && \
                  chmod 644 docker-compose.yml && \
                  mkdir -p logs/nginx && \
                  chmod 755 logs/nginx"

        # Проверяем конфигурацию nginx
        step "Проверка конфигурации nginx..."
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server run --rm nginx nginx -t" || {
            error "Ошибка в конфигурации nginx!"
            ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server logs nginx"
            exit 1
        }

        # Останавливаем текущие контейнеры
        step "Остановка текущих сервисов..."
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server down"

        # Перезапускаем сервисы с новой конфигурацией
        step "Запуск сервисов с конфигурацией домена cock-liz.com..."
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server up -d"

        # Ждем запуска сервисов
        step "Ожидание запуска сервисов..."
        sleep 15

        # Проверяем статус контейнеров
        step "Проверка состояния контейнеров..."
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server ps"

        # Проверяем работу домена
        step "Проверка работы домена cock-liz.com..."

        # Проверяем HTTP редирект
        if ssh_exec "curl -sI http://cock-liz.com | grep -q '301\|302'"; then
            success "HTTP редирект работает"
        else
            warning "HTTP редирект может не работать"
        fi

        # Проверяем HTTPS
        if ssh_exec "curl -sk https://cock-liz.com/health | grep -q 'OK\|healthy'"; then
            success "HTTPS соединение работает"
        else
            warning "HTTPS может не работать корректно"
        fi

        # Проверяем логи nginx
        step "Проверка логов nginx..."
        ssh_exec "cd '$SERVER_PATH' && docker-compose --env-file .env.server logs --tail=20 nginx"

        # Показываем информацию о развертывании
        info "=== Информация о развертывании домена ==="
        info "Домен: https://cock-liz.com"
        info "API документация: https://cock-liz.com/docs"
        info "Проверка состояния: https://cock-liz.com/health"
        info "SSL сертификаты обновлены"
        info "Nginx конфигурация применена"

    else
        # Локальное развертывание
        step "Локальное развертывание конфигурации домена..."

        # Создаем директории
        mkdir -p logs/nginx

        # Проверяем конфигурацию nginx
        step "Проверка конфигурации nginx..."
        cd "$PROJECT_DIR"
        docker-compose --env-file "$ENV_FILE" run --rm nginx nginx -t || {
            error "Ошибка в конфигурации nginx!"
            exit 1
        }

        # Перезапускаем сервисы
        step "Перезапуск сервисов с новой конфигурацией..."
        docker-compose --env-file "$ENV_FILE" down
        docker-compose --env-file "$ENV_FILE" up -d

        info "Локальная конфигурация домена применена"
        info "Доступ: https://localhost"
    fi

    success "Развертывание конфигурации домена cock-liz.com завершено"

    # Финальная проверка
    step "Финальная проверка состояния..."
    check_status
}

# Проверка статуса домена cock-liz.com
check_domain_status() {
    echo "🔍 Проверка статуса домена cock-liz.com"
    if [ "$REMOTE_MODE" = "true" ]; then
        echo "Сервер: $SERVER_IP"
    else
        echo "Локальная проверка"
    fi
    echo "============================================"

    check_dependencies

    local path="$PROJECT_DIR"
    local env_flag=""

    if [ "$REMOTE_MODE" = "true" ]; then
        path="$SERVER_PATH"
        env_flag="--env-file $path/.env.server"
    else
        env_flag="--env-file $path/.env"
    fi

    # Проверяем наличие файлов конфигурации
    step "Проверка файлов конфигурации домена..."

    if [ "$REMOTE_MODE" = "true" ]; then
        # Проверка на сервере
        if ssh_exec "[ -f '$path/nginx/nginx.conf' ]"; then
            success "nginx.conf найден"
        else
            error "nginx.conf отсутствует"
        fi

        if ssh_exec "[ -f '$path/nginx/ssl/cert.pem' ] && [ -f '$path/nginx/ssl/key.pem' ]"; then
            success "SSL сертификаты найдены"
            # Проверяем срок действия сертификата
            ssh_exec "cd '$path' && openssl x509 -in nginx/ssl/cert.pem -noout -enddate" || true
        else
            warning "SSL сертификаты отсутствуют"
        fi

        if ssh_exec "[ -f '$path/.env.server' ]"; then
            success ".env.server найден"
            # Показываем основные настройки
            info "Основные настройки домена:"
            ssh_exec "cd '$path' && grep -E '^(ADMIN_EMAIL|APP_TITLE)' .env.server" || true
        else
            warning ".env.server отсутствует"
        fi
    else
        # Локальная проверка
        [ -f "$path/nginx/nginx.conf" ] && success "nginx.conf найден" || error "nginx.conf отсутствует"
        [ -f "$path/nginx/ssl/cert.pem" ] && [ -f "$path/nginx/ssl/key.pem" ] && success "SSL сертификаты найдены" || warning "SSL сертификаты отсутствуют"
        [ -f "$path/.env" ] && success ".env найден" || warning ".env отсутствует"
    fi

    # Проверяем статус контейнеров
    step "Проверка состояния контейнеров..."
    execute_cmd "cd '$path' && docker-compose $env_flag ps"

    # Проверяем конфигурацию nginx
    step "Проверка конфигурации nginx..."
    if execute_cmd "cd '$path' && docker-compose $env_flag exec nginx nginx -t 2>/dev/null"; then
        success "Конфигурация nginx корректна"
    else
        warning "Проблемы с конфигурацией nginx"
        execute_cmd "cd '$path' && docker-compose $env_flag logs --tail=10 nginx"
    fi

    # Проверяем доступность домена
    if [ "$REMOTE_MODE" = "true" ]; then
        step "Проверка доступности домена cock-liz.com..."

        # Проверяем HTTP -> HTTPS редирект
        if ssh_exec "curl -sI http://cock-liz.com | head -1 | grep -q '301\|302'"; then
            success "HTTP редирект работает"
        else
            warning "HTTP редирект не работает"
        fi

        # Проверяем HTTPS доступность
        if ssh_exec "curl -sk https://cock-liz.com/health | grep -q 'OK\|healthy'"; then
            success "HTTPS домен доступен"
        else
            warning "HTTPS домен недоступен"
        fi

        # Проверяем www редирект
        if ssh_exec "curl -sI https://www.cock-liz.com | head -1 | grep -q '301\|302'"; then
            success "WWW редирект работает"
        else
            warning "WWW редирект не работает"
        fi

        # Проверяем API endpoints
        step "Проверка API endpoints..."

        if ssh_exec "curl -sk https://cock-liz.com/docs | grep -q 'OpenAPI\|Swagger'"; then
            success "API документация доступна"
        else
            warning "API документация недоступна"
        fi

        # Показываем последние логи nginx
        step "Последние записи в логах nginx..."
        ssh_exec "cd '$path' && docker-compose $env_flag logs --tail=5 nginx"

        info "=== Ссылки для проверки ==="
        info "Основной домен: https://cock-liz.com"
        info "API документация: https://cock-liz.com/docs"
        info "Проверка состояния: https://cock-liz.com/health"
        info "Админ панель: https://cock-liz.com/admin"
    else
        info "Локальный доступ: https://localhost"
    fi

    success "Проверка статуса домена завершена"
}

# Обработка аргументов
COMMAND="deploy"
FORCE_MODE="false"
DEBUG_MODE="false"
SERVICE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        deploy|update|migrate|create-admin|start|stop|restart|status|logs|backup|rollback|diagnose|cleanup|deploy-domain|check-domain|help)
            COMMAND="$1"
            shift
            ;;
        --remote)
            REMOTE_MODE="true"
            shift
            ;;
        --force)
            FORCE_MODE="true"
            shift
            ;;
        --debug)
            DEBUG_MODE="true"
            set -x
            shift
            ;;
        -h|--help)
            COMMAND="help"
            shift
            ;;
        *)
            if [ "$COMMAND" = "logs" ]; then
                SERVICE_NAME="$1"
            fi
            shift
            ;;
    esac
done

# Выполнение команды
case $COMMAND in
    deploy)
        full_deploy
        ;;
    update)
        quick_update
        ;;
    migrate)
        check_dependencies
        run_migrations
        ;;
    create-admin)
        check_dependencies
        create_admin
        ;;
    start)
        check_dependencies
        start_services
        ;;
    stop)
        check_dependencies
        stop_services
        ;;
    restart)
        check_dependencies
        restart_services
        ;;
    status)
        check_dependencies
        check_status
        ;;
    logs)
        check_dependencies
        show_logs "$SERVICE_NAME"
        ;;
    backup)
        check_dependencies
        backup_database
        ;;
    rollback)
        check_dependencies
        rollback
        ;;
    cleanup)
        check_dependencies
        cleanup
        ;;
    diagnose)
        check_dependencies
        diagnose_server
        ;;
    deploy-domain)
        check_dependencies
        deploy_domain
        ;;
    check-domain)
        check_dependencies
        check_domain_status
        ;;
    help)
        show_help
        ;;
    *)
        error "Неизвестная команда: $COMMAND"
        show_help
        exit 1
        ;;
esac
