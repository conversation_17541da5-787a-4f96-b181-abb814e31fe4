-- Инициализация базы данных для SMS Proxy Service

-- Создание пользователя для приложения (если не существует)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'sms_proxy_user') THEN
        CREATE USER sms_proxy_user WITH PASSWORD 'sms_proxy_strong_password_2024';
    END IF;
END
$$;

-- Предоставление прав пользователю
GRANT ALL PRIVILEGES ON DATABASE sms_proxy TO sms_proxy_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO sms_proxy_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO sms_proxy_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO sms_proxy_user;

-- Установка прав по умолчанию для новых объектов
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO sms_proxy_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO sms_proxy_user; 