"""add_last_login_to_users

Revision ID: c38a4bc07b3d
Revises: fad25ac75523
Create Date: 2025-05-24 11:58:42.033279

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "c38a4bc07b3d"
down_revision: Union[str, None] = "fad25ac75523"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Проверяем существование колонки перед добавлением
    conn = op.get_bind()

    # Проверяем наличие колонки last_login в таблице users
    result = conn.execute(
        sa.text(
            """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'last_login'
    """
        )
    )

    if not result.fetchone():
        # Колонка не существует, добавляем её
        op.add_column("users", sa.Column("last_login", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "last_login")
    # ### end Alembic commands ###
