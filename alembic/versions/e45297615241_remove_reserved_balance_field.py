"""remove_reserved_balance_field

Revision ID: e45297615241
Revises: ce0adb336fa5
Create Date: 2025-05-25 00:24:03.078413

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "e45297615241"
down_revision: Union[str, None] = "ce0adb336fa5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Проверяем существование колонок перед удалением
    conn = op.get_bind()

    # Проверяем и удаляем updated_at если существует
    result = conn.execute(
        sa.text(
            """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'updated_at'
    """
        )
    )
    if result.fetchone():
        op.drop_column("users", "updated_at")

    # Проверяем и удаляем reserved_balance если существует
    result = conn.execute(
        sa.text(
            """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'reserved_balance'
    """
        )
    )
    if result.fetchone():
        op.drop_column("users", "reserved_balance")

    # НЕ удаляем last_login так как она нужна в модели
    # op.drop_column('users', 'last_login')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column(
            "last_login", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "reserved_balance",
            sa.NUMERIC(precision=10, scale=2),
            autoincrement=False,
            nullable=False,
        ),
    )
    op.add_column(
        "users",
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=False,
        ),
    )
    # ### end Alembic commands ###
