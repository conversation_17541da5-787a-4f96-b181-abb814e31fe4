"""remove_auto_cancel_minutes_field

Revision ID: ce0adb336fa5
Revises: 484a9f9a8fa6
Create Date: 2025-05-25 00:23:29.692880

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ce0adb336fa5'
down_revision: Union[str, None] = '484a9f9a8fa6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('settings', 'auto_cancel_minutes')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('settings', sa.Column('auto_cancel_minutes', sa.INTEGER(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
