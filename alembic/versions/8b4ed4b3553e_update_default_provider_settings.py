"""Update default provider settings

Revision ID: 8b4ed4b3553e
Revises: c38a4bc07b3d
Create Date: 2025-05-24 12:29:21.830193

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8b4ed4b3553e'
down_revision: Union[str, None] = 'c38a4bc07b3d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Обновляем существующие записи настроек в соответствии с DEFAULT_PROVIDER=firefox
    # Устанавливаем use_firefox=True, use_smslive=False для всех существующих записей
    op.execute("""
        UPDATE settings 
        SET use_firefox = true, use_smslive = false 
        WHERE id IS NOT NULL
    """)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Возвращаем обратно: включаем оба провайдера
    op.execute("""
        UPDATE settings 
        SET use_firefox = true, use_smslive = true 
        WHERE id IS NOT NULL
    """)
    
    # ### end Alembic commands ###
