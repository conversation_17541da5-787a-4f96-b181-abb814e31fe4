"""add_sms_providers_table

Revision ID: a74ff3ffe80c
Revises: d81cd4f80945
Create Date: 2025-05-27 23:23:06.683972

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "a74ff3ffe80c"
down_revision: Union[str, None] = "d81cd4f80945"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sms_providers",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "name",
            sa.String(length=50),
            nullable=False,
            comment="Уникальное имя провайдера",
        ),
        sa.Column(
            "display_name",
            sa.String(length=100),
            nullable=False,
            comment="Отображаемое имя для админки",
        ),
        sa.Column(
            "api_url",
            sa.String(length=500),
            nullable=False,
            comment="URL API провайдера",
        ),
        sa.Column(
            "api_key",
            sa.String(length=500),
            nullable=True,
            comment="API ключ для SMSActivate API",
        ),
        sa.Column(
            "username",
            sa.String(length=100),
            nullable=True,
            comment="Логин для Firefox API",
        ),
        sa.Column(
            "password",
            sa.String(length=500),
            nullable=True,
            comment="Пароль для Firefox API",
        ),
        sa.Column(
            "provider_type",
            sa.String(length=30),
            nullable=False,
            comment="Тип провайдера",
        ),
        sa.Column(
            "api_format",
            sa.String(length=20),
            nullable=False,
            comment="Формат API (firefox_api, smsactivate_api)",
        ),
        sa.Column(
            "is_active", sa.Boolean(), nullable=True, comment="Активен ли провайдер"
        ),
        sa.Column(
            "priority", sa.Integer(), nullable=True, comment="Приоритет (1 = высший)"
        ),
        sa.Column(
            "max_requests_per_minute",
            sa.Integer(),
            nullable=True,
            comment="Лимит запросов в минуту",
        ),
        sa.Column(
            "timeout_seconds",
            sa.Integer(),
            nullable=True,
            comment="Таймаут запросов в секундах",
        ),
        sa.Column(
            "settings",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="Дополнительные настройки провайдера",
        ),
        sa.Column(
            "last_test_at",
            sa.DateTime(),
            nullable=True,
            comment="Последнее тестирование",
        ),
        sa.Column(
            "last_test_result",
            sa.String(length=20),
            nullable=True,
            comment="Результат последнего теста",
        ),
        sa.Column(
            "test_error_message",
            sa.Text(),
            nullable=True,
            comment="Сообщение об ошибке теста",
        ),
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            nullable=True,
            comment="Дата создания",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            nullable=True,
            comment="Дата обновления",
        ),
        sa.Column(
            "created_by_user_id",
            sa.Integer(),
            nullable=True,
            comment="Создал пользователь",
        ),
        sa.ForeignKeyConstraint(
            ["created_by_user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.alter_column(
        "settings",
        "sms_timeout",
        existing_type=sa.INTEGER(),
        server_default=None,
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "default_provider",
        existing_type=sa.VARCHAR(length=50),
        server_default=None,
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "debug_mode",
        existing_type=sa.BOOLEAN(),
        server_default=None,
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "log_level",
        existing_type=sa.VARCHAR(length=20),
        server_default=None,
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "settings",
        "log_level",
        existing_type=sa.VARCHAR(length=20),
        server_default=sa.text("'INFO'::character varying"),
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "debug_mode",
        existing_type=sa.BOOLEAN(),
        server_default=sa.text("false"),
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "default_provider",
        existing_type=sa.VARCHAR(length=50),
        server_default=sa.text("'smslive'::character varying"),
        existing_nullable=False,
    )
    op.alter_column(
        "settings",
        "sms_timeout",
        existing_type=sa.INTEGER(),
        server_default=sa.text("1200"),
        existing_nullable=False,
    )
    op.drop_table("sms_providers")
    # ### end Alembic commands ###
