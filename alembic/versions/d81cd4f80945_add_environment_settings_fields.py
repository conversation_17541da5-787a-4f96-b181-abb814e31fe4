"""add_environment_settings_fields

Revision ID: d81cd4f80945
Revises: add_ip_address_001
Create Date: 2025-05-27 11:22:48.919676

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "d81cd4f80945"
down_revision: Union[str, None] = "add_ip_address_001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Добавление новых полей для управления переменными окружения"""

    # Добавляем новые поля для настроек переменных окружения
    op.add_column(
        "settings",
        sa.Column("sms_timeout", sa.Integer(), nullable=False, server_default="1200"),
    )
    op.add_column(
        "settings", sa.Column("firefox_api_name", sa.String(255), nullable=True)
    )
    op.add_column(
        "settings", sa.Column("firefox_password", sa.String(255), nullable=True)
    )
    op.add_column(
        "settings", sa.Column("smslive_api_key", sa.String(255), nullable=True)
    )
    op.add_column(
        "settings",
        sa.Column(
            "default_provider", sa.String(50), nullable=False, server_default="smslive"
        ),
    )
    op.add_column(
        "settings",
        sa.Column("debug_mode", sa.Boolean(), nullable=False, server_default="false"),
    )
    op.add_column(
        "settings",
        sa.Column("log_level", sa.String(20), nullable=False, server_default="INFO"),
    )


def downgrade() -> None:
    """Удаление новых полей для управления переменными окружения"""

    # Удаляем добавленные поля
    op.drop_column("settings", "log_level")
    op.drop_column("settings", "debug_mode")
    op.drop_column("settings", "default_provider")
    op.drop_column("settings", "smslive_api_key")
    op.drop_column("settings", "firefox_password")
    op.drop_column("settings", "firefox_api_name")
    op.drop_column("settings", "sms_timeout")
