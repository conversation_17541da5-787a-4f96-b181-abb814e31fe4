"""add_balance_constraints_for_race_condition_protection

Revision ID: 484a9f9a8fa6
Revises: ddeeed06b1cf
Create Date: 2025-05-24 14:19:54.645665

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '484a9f9a8fa6'
down_revision: Union[str, None] = 'ddeeed06b1cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'reserved_balance',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               server_default=None,
               existing_nullable=False)
    
    # Добавляем ограничения целостности для защиты от race conditions
    op.create_check_constraint(
        'check_balance_non_negative',
        'users',
        'balance >= 0'
    )
    
    op.create_check_constraint(
        'check_reserved_balance_non_negative', 
        'users',
        'reserved_balance >= 0'
    )
    
    op.create_check_constraint(
        'check_balance_sufficient_for_reserved',
        'users', 
        'balance >= reserved_balance'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Удаляем ограничения целостности
    op.drop_constraint('check_balance_sufficient_for_reserved', 'users', type_='check')
    op.drop_constraint('check_reserved_balance_non_negative', 'users', type_='check')
    op.drop_constraint('check_balance_non_negative', 'users', type_='check')
    
    op.alter_column('users', 'reserved_balance',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               server_default=sa.text('0.00'),
               existing_nullable=False)
    # ### end Alembic commands ###
