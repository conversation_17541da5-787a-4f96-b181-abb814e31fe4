"""remove_deprecated_provider_settings_fields

Revision ID: 60a55eb5d937
Revises: a74ff3ffe80c
Create Date: 2025-05-28 12:21:36.896654

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "60a55eb5d937"
down_revision: Union[str, None] = "a74ff3ffe80c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Удаляем устаревшие поля провайдеров из таблицы настроек"""

    # Удаляем поля API ключей провайдеров (теперь в таблице sms_providers)
    try:
        op.drop_column("settings", "firefox_api_name")
    except Exception:
        pass  # Колонка может уже не существовать

    try:
        op.drop_column("settings", "firefox_password")
    except Exception:
        pass

    try:
        op.drop_column("settings", "smslive_api_key")
    except Exception:
        pass

    # Удаляем устаревшие системные настройки провайдеров
    try:
        op.drop_column("settings", "default_provider")
    except Exception:
        pass


def downgrade() -> None:
    """Восстанавливаем удаленные поля (если нужно откатиться)"""

    # Восстанавливаем поля API ключей провайдеров
    op.add_column(
        "settings", sa.Column("firefox_api_name", sa.String(255), nullable=True)
    )
    op.add_column(
        "settings", sa.Column("firefox_password", sa.String(255), nullable=True)
    )
    op.add_column(
        "settings", sa.Column("smslive_api_key", sa.String(255), nullable=True)
    )

    # Восстанавливаем системные настройки провайдеров
    op.add_column(
        "settings",
        sa.Column(
            "default_provider", sa.String(50), nullable=False, server_default="smslive"
        ),
    )
