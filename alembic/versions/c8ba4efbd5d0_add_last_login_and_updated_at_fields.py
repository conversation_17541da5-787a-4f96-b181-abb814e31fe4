"""add_last_login_and_updated_at_fields

Revision ID: c8ba4efbd5d0
Revises: e45297615241
Create Date: 2025-05-25 00:30:45.677177

"""
from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "c8ba4efbd5d0"
down_revision: Union[str, None] = "e45297615241"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Проверяем существование колонок перед добавлением
    conn = op.get_bind()

    # Проверяем наличие колонки updated_at
    result = conn.execute(
        sa.text(
            """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'updated_at'
    """
        )
    )

    if not result.fetchone():
        op.add_column(
            "users",
            sa.Column(
                "updated_at",
                sa.DateTime(),
                server_default=sa.text("now()"),
                nullable=False,
            ),
        )

    # Проверяем наличие колонки last_login
    result = conn.execute(
        sa.text(
            """
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'users' AND column_name = 'last_login'
    """
        )
    )

    if not result.fetchone():
        op.add_column("users", sa.Column("last_login", sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "last_login")
    op.drop_column("users", "updated_at")
    # ### end Alembic commands ###
