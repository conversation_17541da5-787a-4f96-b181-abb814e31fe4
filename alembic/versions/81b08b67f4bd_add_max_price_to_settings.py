"""add_max_price_to_settings

Revision ID: 81b08b67f4bd
Revises: c8ba4efbd5d0
Create Date: 2025-05-25 00:59:47.140595

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '81b08b67f4bd'
down_revision: Union[str, None] = 'c8ba4efbd5d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('settings', sa.Column('max_price', sa.Numeric(precision=10, scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('settings', 'max_price')
    # ### end Alembic commands ###
