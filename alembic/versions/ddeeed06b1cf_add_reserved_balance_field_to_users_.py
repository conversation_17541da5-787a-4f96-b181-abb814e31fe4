"""Add reserved_balance field to users table

Revision ID: ddeeed06b1cf
Revises: 8b4ed4b3553e
Create Date: 2025-05-24 14:12:33.121169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ddeeed06b1cf'
down_revision: Union[str, None] = '8b4ed4b3553e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('reserved_balance', sa.Numeric(precision=10, scale=2), nullable=False, server_default='0.00'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'reserved_balance')
    # ### end Alembic commands ###
