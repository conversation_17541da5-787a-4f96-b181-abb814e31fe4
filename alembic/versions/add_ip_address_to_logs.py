"""add_ip_address_to_logs

Revision ID: add_ip_address_001
Revises: 81b08b67f4bd
Create Date: 2025-05-26 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_ip_address_001'
down_revision: Union[str, None] = '81b08b67f4bd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Добавление поля ip_address в таблицу logs"""
    op.add_column('logs', sa.Column('ip_address', sa.String(45), nullable=True))


def downgrade() -> None:
    """Удаление поля ip_address из таблицы logs"""
    op.drop_column('logs', 'ip_address') 