# Docker Compose файл для SMS Proxy Service

services:
  # PostgreSQL база данных
  postgres:
    image: postgres:15-alpine
    container_name: sms_proxy_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sms_proxy}
      POSTGRES_USER: ${POSTGRES_USER:-sms_proxy_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-sms_proxy_strong_password_2024}
    ports:
      - "127.0.0.1:5432:5432"  # Привязываем только к localhost
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - sms_proxy_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-sms_proxy_user} -d ${POSTGRES_DB:-sms_proxy}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis удален - планировщик задач больше не используется

  # Основное FastAPI приложение
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sms_proxy_api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${POSTGRES_USER:-sms_proxy_user}:${POSTGRES_PASSWORD:-sms_proxy_strong_password_2024}@postgres:5432/${POSTGRES_DB:-sms_proxy}
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=${DEBUG:-false}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - SMS_TIMEOUT=${SMS_TIMEOUT:-30}
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      # Статические файлы остаются в контейнере
    networks:
      - sms_proxy_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Celery Worker и Beat удалены - планировщик задач больше не используется

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: sms_proxy_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      # Монтируем статические файлы напрямую из хоста
      - ./app/static:/app/static:ro
    depends_on:
      - api
    networks:
      - sms_proxy_network

volumes:
  postgres_data:
    driver: local
  # Убираем static_files volume - используем прямое монтирование

networks:
  sms_proxy_network:
    driver: bridge
