"""
SMS Proxy Service - Main Application Entry Point

Основная точка входа для FastAPI приложения SMS прокси-сервиса.
Настраивает маршруты, middleware, CORS и документацию API.
"""

from __future__ import annotations

from contextlib import asynccontextmanager
import os

from fastapi import Depends, FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy import text
import uvicorn

from app.api.admin import router as admin_router
from app.api.providers import router as providers_router
from app.api.sms_activate import router as sms_activate_router
from app.core.config import settings
from app.core.database import Base, engine
from app.core.logger import critical, get_logger, info
from app.core.middleware import (
    RateLimitMiddleware,
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
)
from app.core.security import get_current_admin_by_header

# Инициализация единого логгера
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle events для приложения"""
    # Startup
    info("Запуск SMS Proxy Service...")

    # В продакшн миграции применяются через Alembic
    # Здесь только проверяем подключение к БД
    try:
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        info("Подключение к базе данных установлено")
    except Exception as e:
        critical(f"Ошибка подключения к базе данных: {e}")
        raise

    info("База данных инициализирована")
    info("SMS Proxy Service запущен успешно")

    yield

    # Shutdown
    info("Остановка SMS Proxy Service...")
    await engine.dispose()
    info("SMS Proxy Service остановлен")


# Создание FastAPI приложения
app = FastAPI(
    title="SMS Activation API",
    description="""
    ## API для получения номеров и SMS активации

    Наш собственный API для получения виртуальных номеров телефонов и SMS кодов активации различных сервисов.

    ### Основные действия:

    * `getBalance` - получение баланса аккаунта
    * `getNumber` - заказ номера для активации
    * `getStatus` - получение статуса активации и SMS кода
    * `setStatus` - управление активацией (отмена, повтор)
    * `getPrices` - получение цен на номера
    * `getNumbersStatus` - проверка доступности номеров

    ### Использование:

    Все запросы выполняются к эндпоинту `/stubs/handler_api.php` с параметрами:
    - `api_key` - ваш API ключ
    - `action` - действие (getBalance, getNumber, getStatus, setStatus, getPrices, getNumbersStatus)
    - `service` - код сервиса (для getNumber)
    - `country` - код страны (опционально)
    - `id` - ID активации (для getStatus, setStatus)
    - `status` - статус для установки (для setStatus)

    ### Пример:
    ```
    GET /stubs/handler_api.php?api_key=YOUR_KEY&action=getBalance
    GET /stubs/handler_api.php?api_key=YOUR_KEY&action=getNumber&service=wb&country=0
    ```
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

# Middleware для защиты от DDoS и безопасности
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitMiddleware, max_requests=100, time_window=60)

# Middleware для безопасности
if not settings.debug:
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.allowed_hosts)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts if not settings.debug else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Глобальный обработчик исключений
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Глобальный обработчик исключений"""
    critical(f"Неожиданная ошибка: {exc}", exc_info=True)

    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={
                "error": "INTERNAL_ERROR",
                "message": str(exc),
                "detail": "Внутренняя ошибка сервера",
            },
        )

    return JSONResponse(
        status_code=500,
        content={"error": "INTERNAL_ERROR", "message": "Внутренняя ошибка сервера"},
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Обработчик HTTP исключений"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP_ERROR",
            "message": exc.detail,
            "status_code": exc.status_code,
        },
    )


# Маршруты - только основной API


@app.get("/health", include_in_schema=False)
async def health_check():
    """Health check endpoint для мониторинга состояния сервиса"""
    try:
        # Проверяем подключение к базе данных
        from app.core.database import engine

        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "service": "SMS Proxy Service",
            "version": "1.0.0",
            "database": "connected",
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "SMS Proxy Service",
                "version": "1.0.0",
                "database": "disconnected",
                "error": str(e),
            },
        )


# Статические файлы для админки
if os.path.exists("app/static"):
    app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Подключение роутеров
app.include_router(sms_activate_router, prefix="/stubs", tags=["SMS API"])

app.include_router(
    admin_router,
    prefix="/api/admin",
    tags=["Admin API"],
    include_in_schema=False,  # Скрываем админский API из публичной документации
)

app.include_router(
    providers_router,
    prefix="/api",
    tags=["Providers API"],
    include_in_schema=False,  # Скрываем API управления провайдерами из публичной документации
)


@app.get("/admin", include_in_schema=False)
async def admin_panel():
    """Главная страница админ панели"""
    try:
        with open("app/templates/admin/index.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>Админ панель не найдена</h1><p>Файл шаблона не существует</p>",
            status_code=404,
        )


@app.get("/admin/docs", include_in_schema=False)
async def admin_docs():
    """Админская документация с формой авторизации"""
    try:
        with open("app/templates/admin/docs.html", "r", encoding="utf-8") as f:
            content = f.read()
        return HTMLResponse(content=content)
    except FileNotFoundError:
        return HTMLResponse(
            content="<h1>Шаблон документации не найден</h1><p>Файл app/templates/admin/docs.html не существует</p>",
            status_code=404,
        )


@app.post("/admin/verify-api-key", include_in_schema=False)
async def verify_admin_api_key(current_admin=Depends(get_current_admin_by_header)):
    """Проверка API ключа администратора"""
    return {"success": True, "message": "API ключ валидный"}


@app.get("/admin/openapi.json", include_in_schema=False)
async def admin_openapi(current_admin=Depends(get_current_admin_by_header)):
    """OpenAPI схема для админской панели - только для авторизованных администраторов"""
    from fastapi.openapi.utils import get_openapi

    # Создаем временное приложение только с админскими роутерами
    temp_app = FastAPI(
        title="SMS Proxy Service - Admin Panel",
        description="Административная панель для управления SMS прокси-сервисом",
        version="1.0.0",
    )
    temp_app.include_router(admin_router, tags=["Admin API"])

    return get_openapi(
        title="SMS Proxy Service - Admin Panel",
        version="1.0.0",
        description="Административная панель для управления SMS прокси-сервисом",
        routes=temp_app.routes,
    )


if __name__ == "__main__":
    # Возвращаем стандартное логирование uvicorn в консоль
    # Убираем только логи о подозрительной активности
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )
