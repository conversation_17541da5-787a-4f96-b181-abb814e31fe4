# Пример конфигурации для SMS прокси-сервиса
# Скопируйте этот файл в .env и измените значения

# Настройки базы данных PostgreSQL для Docker
POSTGRES_DB=sms_proxy
POSTGRES_USER=sms_proxy_user
POSTGRES_PASSWORD=CHANGE_THIS_STRONG_PASSWORD

# URL базы данных для приложения
DATABASE_URL=postgresql+asyncpg://sms_proxy_user:CHANGE_THIS_STRONG_PASSWORD@postgres:5432/sms_proxy

# Настройки безопасности
SECRET_KEY=CHANGE_THIS_TO_VERY_LONG_RANDOM_STRING_AT_LEAST_32_CHARS
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256
ENCRYPTION_KEY="CHANGE_THIS_ENCRYPTION_KEY_TO_32_BASE64_CHARS"

# Настройки приложения
APP_TITLE="SMS Proxy Service"
APP_DESCRIPTION="SMS Proxy Service for virtual phone number providers"
APP_VERSION="1.0.0"
DEBUG=false

# Настройки провайдеров перенесены в базу данных
# Управление через админку: /admin -> Провайдеры

# Общие настройки SMS системы
SMS_TIMEOUT=30

# Настройки админ-панели
ADMIN_EMAIL=<EMAIL>

# Логирование
LOG_LEVEL=INFO

# Настройки для продакшн
ENVIRONMENT=production
