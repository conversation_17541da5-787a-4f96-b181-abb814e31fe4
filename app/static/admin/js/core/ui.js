/**
 * UI утилиты для админ панели
 * Общие функции для работы с интерфейсом
 */
class UIManager {
    constructor() {
        this.notifications = [];
        this.modals = new Map();
    }

    /**
     * Инициализация UI менеджера
     */
    init() {
        this.setupMobileNavigation();
        this.setupTouchGestures();
        this.setupGlobalEventListeners();
    }

    /**
     * Показать уведомление об успехе
     * @param {string} message - Текст сообщения
     * @param {number} duration - Длительность показа в миллисекундах
     */
    showSuccess(message, duration = 5000) {
        this.showNotification(message, 'success', duration);
    }

    /**
     * Показать уведомление об ошибке
     * @param {string} message - Текст сообщения
     * @param {number} duration - Длительность показа в миллисекундах
     */
    showError(message, duration = 5000) {
        this.showNotification(message, 'danger', duration);
    }

    /**
     * Показать информационное уведомление
     * @param {string} message - Текст сообщения
     * @param {number} duration - Длительность показа в миллисекундах
     */
    showInfo(message, duration = 5000) {
        this.showNotification(message, 'info', duration);
    }

    /**
     * Показать предупреждение
     * @param {string} message - Текст сообщения
     * @param {number} duration - Длительность показа в миллисекундах
     */
    showWarning(message, duration = 5000) {
        this.showNotification(message, 'warning', duration);
    }

    /**
     * Показать уведомление
     * @param {string} message - Текст сообщения
     * @param {string} type - Тип уведомления (success, danger, info, warning)
     * @param {number} duration - Длительность показа в миллисекундах
     */
    showNotification(message, type = 'info', duration = 5000) {
        const id = 'notification-' + Date.now();
        const notification = {
            id,
            message,
            type,
            duration
        };

        this.notifications.push(notification);

        // Создаем HTML для уведомления
        const notificationHtml = `
            <div id="${id}" class="alert alert-${type} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="bi bi-${this.getIconForType(type)}"></i> ${message}
                <button type="button" class="btn-close" onclick="uiManager.hideNotification('${id}')"></button>
            </div>
        `;

        // Добавляем уведомление в DOM
        document.body.insertAdjacentHTML('beforeend', notificationHtml);

        // Автоматически скрываем уведомление
        if (duration > 0) {
            setTimeout(() => this.hideNotification(id), duration);
        }
    }

    /**
     * Скрыть уведомление
     * @param {string} id - ID уведомления
     */
    hideNotification(id) {
        const element = document.getElementById(id);
        if (element) {
            element.classList.remove('show');
            setTimeout(() => {
                element.remove();
                this.notifications = this.notifications.filter(n => n.id !== id);
            }, 150);
        }
    }

    /**
     * Получить иконку для типа уведомления
     * @param {string} type - Тип уведомления
     * @returns {string} Класс иконки Bootstrap Icons
     */
    getIconForType(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            info: 'info-circle',
            warning: 'exclamation-triangle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Показать модальное окно подтверждения
     * @param {string} title - Заголовок окна
     * @param {string} message - Текст сообщения
     * @param {Function} onConfirm - Колбэк при подтверждении
     * @param {Function} onCancel - Колбэк при отмене
     */
    showConfirmModal(title, message, onConfirm, onCancel = null) {
        const modalId = 'confirm-modal-' + Date.now();

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-danger" id="${modalId}-confirm">Подтвердить</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Добавляем модальное окно в DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        const modal = new bootstrap.Modal(document.getElementById(modalId));

        // Обработчик подтверждения
        document.getElementById(`${modalId}-confirm`).addEventListener('click', () => {
            modal.hide();
            if (onConfirm) onConfirm();
        });

        // Обработчик отмены
        document.getElementById(modalId).addEventListener('hidden.bs.modal', () => {
            document.getElementById(modalId).remove();
            if (onCancel) onCancel();
        });

        modal.show();
        return modal;
    }

    /**
     * Настройка мобильной навигации
     */
    setupMobileNavigation() {
        // Мобильное меню
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMobileSidebar();
            });
        }

        // Закрытие меню при клике на overlay
        const mobileOverlay = document.getElementById('mobileOverlay');
        if (mobileOverlay) {
            mobileOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }
    }

    /**
     * Настройка глобальных обработчиков событий
     */
    setupGlobalEventListeners() {
        // Закрытие мобильного меню при клике вне его области
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            if (sidebar && sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                sidebarToggle && !sidebarToggle.contains(e.target)) {
                this.closeMobileSidebar();
            }
        });

        // Закрытие мобильного меню при нажатии Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileSidebar();
            }
        });
    }

    /**
     * Переключение состояния мобильного бокового меню
     */
    toggleMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar && sidebar.classList.contains('show')) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    /**
     * Открытие мобильного бокового меню
     */
    openMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('mobileOverlay');

        if (sidebar) {
            sidebar.classList.add('show');
        }

        if (overlay) {
            overlay.classList.add('show');
        }

        // Блокируем скролл страницы
        document.body.classList.add('sidebar-open');
    }

    /**
     * Закрытие мобильного бокового меню
     */
    closeMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('mobileOverlay');

        if (sidebar && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }

        if (overlay && overlay.classList.contains('show')) {
            overlay.classList.remove('show');
        }

        // Разрешаем скролл страницы
        document.body.classList.remove('sidebar-open');
    }

    /**
     * Настройка touch жестов для мобильных устройств
     */
    setupTouchGestures() {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;

        const sidebar = document.querySelector('.sidebar');

        // Обработка начала касания
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });

        // Обработка окончания касания
        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = Math.abs(touchEndY - touchStartY);

            // Проверяем что это горизонтальный свайп
            if (Math.abs(deltaX) > deltaY && Math.abs(deltaX) > 50) {
                // Свайп влево - закрываем меню (если оно открыто)
                if (deltaX < -50 && sidebar && sidebar.classList.contains('show')) {
                    this.closeMobileSidebar();
                }
                // Свайп вправо от левого края - открываем меню (если оно закрыто)
                else if (deltaX > 50 && touchStartX < 50 && sidebar && !sidebar.classList.contains('show')) {
                    this.openMobileSidebar();
                }
            }
        }, { passive: true });
    }

    /**
     * Показать индикатор загрузки в контейнере
     * @param {string} containerId - ID контейнера
     * @param {string} message - Текст сообщения
     */
    showLoadingInContainer(containerId, message = 'Загрузка...') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">${message}</span>
                    </div>
                    <p class="mt-2 text-muted">${message}</p>
                </div>
            `;
        }
    }

    /**
     * Очистить все уведомления
     */
    clearAllNotifications() {
        this.notifications.forEach(notification => {
            this.hideNotification(notification.id);
        });
    }

    /**
     * Получить цвет для статуса
     * @param {string} status - Статус
     * @returns {string} Класс цвета Bootstrap
     */
    getStatusColor(status) {
        const colors = {
            'PENDING': 'warning',
            'WAITING_SMS': 'info',
            'COMPLETED': 'success',
            'CANCELLED': 'danger',
            'EXPIRED': 'secondary',
            'ERROR': 'danger'
        };
        return colors[status] || 'secondary';
    }

    /**
     * Форматирование даты
     * @param {string} dateString - Строка с датой
     * @returns {string} Отформатированная дата
     */
    formatDate(dateString) {
        if (!dateString) return '-';

        try {
            const date = new Date(dateString);
            return date.toLocaleString('ru-RU', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.error('Ошибка форматирования даты:', error);
            return dateString;
        }
    }
}

// Создаем глобальный экземпляр UI менеджера
window.uiManager = new UIManager();
