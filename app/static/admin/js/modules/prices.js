/**
 * Модуль управления ценами
 * Обеспечивает функциональность для работы с ценами в админ панели
 */
class PricesModule {
    constructor() {
        this.isLoading = false;
        this.currentPrices = [];
        this.editingPriceId = null;
        this.countries = [];
        this.services = [];
    }

    /**
     * Инициализация модуля цен
     */
    init() {
        console.log('Инициализация модуля цен');
    }

    /**
     * Загрузка списка цен
     */
    async loadPrices() {
        if (this.isLoading) return;

        console.log('Загрузка цен');
        this.isLoading = true;

        try {
            // Показываем индикатор загрузки
            this.showLoadingIndicator();

            // Загружаем цены через API
            const prices = await apiClient.get(AppConstants.API_ENDPOINTS.PRICES);
            console.log('Цены получены:', prices);

            this.currentPrices = prices;
            this.renderPrices(prices);

        } catch (error) {
            console.error('Ошибка загрузки цен:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Отображение индикатора загрузки
     */
    showLoadingIndicator() {
        const container = document.getElementById('prices-content');
        if (container) {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <span class="ms-2">Загрузка цен...</span>
                </div>
            `;
        }
    }

    /**
     * Обработка ошибки загрузки
     * @param {Error} error - Ошибка загрузки
     */
    handleLoadError(error) {
        const container = document.getElementById('prices-content');
        if (container) {
            let errorMessage = 'Ошибка загрузки данных';

            if (error.message.includes('Не авторизован')) {
                errorMessage = 'Ошибка авторизации. Пожалуйста, войдите в систему заново.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Ошибка соединения с сервером';
            }

            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Ошибка загрузки цен:</strong> ${errorMessage}
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="pricesModule.loadPrices()">
                        <i class="bi bi-arrow-clockwise"></i> Повторить
                    </button>
                </div>
            `;
        }

        // Показываем уведомление об ошибке
        if (window.uiManager) {
            uiManager.showError(`Ошибка загрузки цен: ${error.message}`);
        }
    }

    /**
     * Отображение списка цен
     * @param {Array} prices - Массив цен
     */
    renderPrices(prices) {
        const container = document.getElementById('prices-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-currency-dollar"></i> Список цен</h5>
                    <button class="btn btn-primary" onclick="pricesModule.showPriceModal()">
                        <i class="bi bi-plus"></i> Добавить цену
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Страна</th>
                                    <th>Сервис</th>
                                    <th>Цена</th>
                                    <th>Провайдер</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${prices.map(price => `
                                    <tr>
                                        <td>${price.id}</td>
                                        <td>
                                            ${price.country ? `
                                                <span class="badge bg-info">${price.country.code}</span>
                                                ${escapeHtml(price.country.name)}
                                            ` : `ID: ${price.country_id}`}
                                        </td>
                                        <td>
                                            ${price.service ? `
                                                <code>${price.service.code}</code>
                                                ${escapeHtml(price.service.name)}
                                            ` : `ID: ${price.service_id}`}
                                        </td>
                                        <td>
                                            <strong>${formatPrice(price.price)}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">${escapeHtml(price.provider_name || '-')}</span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1"
                                                    onclick="pricesModule.editPrice(${price.id})"
                                                    title="Редактировать">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="pricesModule.deletePrice(${price.id})"
                                                    title="Удалить">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ${prices.length === 0 ? `
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-currency-dollar" style="font-size: 3rem;"></i>
                            <p class="mt-2">Цены не найдены</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * Показать модальное окно для создания/редактирования цены
     * @param {number|null} priceId - ID цены для редактирования (null для создания)
     */
    async showPriceModal(priceId = null) {
        this.editingPriceId = priceId;
        const isEditing = priceId !== null;

        // Загружаем справочники если они еще не загружены
        await this.loadReferenceData();

        const modalHtml = `
            <div class="modal fade" id="priceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-currency-dollar"></i>
                                ${isEditing ? 'Редактировать цену' : 'Добавить цену'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="priceForm">
                                <div class="mb-3">
                                    <label for="priceCountry" class="form-label">Страна *</label>
                                    <select class="form-select" id="priceCountry" required>
                                        <option value="">Выберите страну</option>
                                        ${this.countries.map(country => `
                                            <option value="${country.id}">${country.name} (${country.code})</option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="priceService" class="form-label">Сервис *</label>
                                    <select class="form-select" id="priceService" required>
                                        <option value="">Выберите сервис</option>
                                        ${this.services.map(service => `
                                            <option value="${service.id}">${service.name} (${service.code})</option>
                                        `).join('')}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="priceValue" class="form-label">Цена (USD) *</label>
                                    <input type="number" class="form-control" id="priceValue"
                                           step="0.01" min="0" placeholder="0.00" required>
                                    <div class="form-text">Цена в долларах США</div>
                                </div>
                                <div class="mb-3">
                                    <label for="priceProvider" class="form-label">Провайдер</label>
                                    <input type="text" class="form-control" id="priceProvider"
                                           placeholder="Название провайдера">
                                    <div class="form-text">Опционально: название провайдера для этой цены</div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="pricesModule.savePrice()">
                                ${isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('priceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные цены
        if (isEditing) {
            this.loadPriceData(priceId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('priceModal'));
        modal.show();
    }

    /**
     * Загрузка справочных данных (страны и сервисы)
     */
    async loadReferenceData() {
        try {
            if (this.countries.length === 0) {
                this.countries = await apiClient.get(AppConstants.API_ENDPOINTS.COUNTRIES);
            }
            if (this.services.length === 0) {
                this.services = await apiClient.get(AppConstants.API_ENDPOINTS.SERVICES);
            }
        } catch (error) {
            console.error('Ошибка загрузки справочных данных:', error);
            uiManager.showError('Ошибка загрузки справочных данных');
        }
    }

    /**
     * Загрузка данных цены для редактирования
     * @param {number} priceId - ID цены
     */
    async loadPriceData(priceId) {
        try {
            const price = this.currentPrices.find(p => p.id === priceId);
            if (price) {
                document.getElementById('priceCountry').value = price.country_id;
                document.getElementById('priceService').value = price.service_id;
                document.getElementById('priceValue').value = price.price;
                document.getElementById('priceProvider').value = price.provider_name || '';
            }
        } catch (error) {
            console.error('Ошибка загрузки данных цены:', error);
            uiManager.showError('Ошибка загрузки данных цены');
        }
    }

    /**
     * Сохранение цены (создание или обновление)
     */
    async savePrice() {
        try {
            const form = document.getElementById('priceForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const priceData = {
                country_id: parseInt(document.getElementById('priceCountry').value),
                service_id: parseInt(document.getElementById('priceService').value),
                price: parseFloat(document.getElementById('priceValue').value),
                provider_name: document.getElementById('priceProvider').value.trim() || null
            };

            console.log('Сохранение цены:', priceData);

            let result;
            if (this.editingPriceId) {
                // Обновляем существующую цену
                result = await apiClient.put(
                    AppConstants.API_ENDPOINTS.PRICE_BY_ID(this.editingPriceId),
                    priceData
                );
                uiManager.showSuccess('Цена успешно обновлена');
            } else {
                // Создаем новую цену
                result = await apiClient.post(AppConstants.API_ENDPOINTS.PRICES, priceData);
                uiManager.showSuccess('Цена успешно создана');
            }

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('priceModal'));
            modal.hide();

            // Перезагружаем список цен
            await this.loadPrices();

        } catch (error) {
            console.error('Ошибка сохранения цены:', error);
            uiManager.showError(`Ошибка сохранения цены: ${error.message}`);
        }
    }

    /**
     * Редактирование цены
     * @param {number} priceId - ID цены
     */
    editPrice(priceId) {
        this.showPriceModal(priceId);
    }

    /**
     * Удаление цены
     * @param {number} priceId - ID цены
     */
    async deletePrice(priceId) {
        const price = this.currentPrices.find(p => p.id === priceId);
        if (!price) return;

        const countryName = price.country ? price.country.name : `ID: ${price.country_id}`;
        const serviceName = price.service ? price.service.name : `ID: ${price.service_id}`;

        const confirmed = await new Promise((resolve) => {
            uiManager.showConfirmModal(
                'Подтверждение удаления',
                `Вы уверены, что хотите удалить цену для "${countryName}" - "${serviceName}"?`,
                () => resolve(true),
                () => resolve(false)
            );
        });

        if (!confirmed) return;

        try {
            await apiClient.delete(AppConstants.API_ENDPOINTS.PRICE_BY_ID(priceId));
            uiManager.showSuccess('Цена успешно удалена');

            // Перезагружаем список цен
            await this.loadPrices();

        } catch (error) {
            console.error('Ошибка удаления цены:', error);
            uiManager.showError(`Ошибка удаления цены: ${error.message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля цен
window.pricesModule = new PricesModule();
