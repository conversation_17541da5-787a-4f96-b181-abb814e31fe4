/**
 * Модуль управления логами системы
 * Обеспечивает функциональность просмотра, фильтрации и очистки логов
 */

class LogsModule {
    constructor() {
        this.currentFilters = {};
        this.pagination = null;
        this.isInitialized = false;
    }

    /**
     * Инициализация модуля логов
     */
    init() {
        if (this.isInitialized) {
            return;
        }

        console.log('Инициализация модуля логов...');
        
        // Инициализируем менеджер пагинации для логов
        this.initializePagination();
        
        this.isInitialized = true;
        console.log('Модуль логов инициализирован');
    }

    /**
     * Инициализация пагинации для логов
     */
    initializePagination() {
        if (!window.PaginationManager) {
            console.error('PaginationManager не найден');
            return;
        }

        this.pagination = new PaginationManager(
            'logs-content',
            async (params) => {
                // Формируем URL с параметрами фильтрации
                const queryParams = new URLSearchParams({
                    skip: params.skip.toString(),
                    limit: params.limit.toString(),
                    ...this.currentFilters
                });

                const logs = await this.apiRequest(`/api/admin/logs?${queryParams}`);
                return logs;
            },
            {
                pageSize: 20,
                pageSizeOptions: [20, 50, 100, 200]
            }
        );

        // Переопределяем метод renderContent для логов
        this.pagination.renderContent = (logs) => {
            this.renderLogsContent(logs);
        };
    }

    /**
     * Загрузка логов с текущими фильтрами
     */
    async loadLogs() {
        if (!this.pagination) {
            console.error('Пагинация не инициализирована');
            return;
        }

        try {
            await this.pagination.loadData();
        } catch (error) {
            console.error('Ошибка загрузки логов:', error);
            this.showError('Ошибка загрузки логов');
        }
    }

    /**
     * Отображение содержимого логов
     */
    renderLogsContent(logs) {
        const container = document.getElementById('logs-content');
        if (!container) {
            console.error('Контейнер logs-content не найден');
            return;
        }

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-journal-text"></i> Логи системы</h5>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="logsModule.showFiltersModal()">
                            <i class="bi bi-funnel"></i> Фильтры
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="logsModule.showClearLogsModal()">
                            <i class="bi bi-trash"></i> Очистить логи
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="logsModule.loadLogs()">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                    </div>
                </div>
                
                <!-- Панель активных фильтров -->
                ${this.renderActiveFilters()}
                
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 80px;">ID</th>
                                    <th style="width: 120px;">Пользователь</th>
                                    <th style="width: 150px;">Действие</th>
                                    <th>Детали</th>
                                    <th style="width: 120px;">IP адрес</th>
                                    <th style="width: 160px;">Дата</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${logs.length > 0 ? logs.map(log => this.renderLogRow(log)).join('') : this.renderEmptyState()}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Находим контейнер для контента (исключая пагинацию)
        let contentContainer = container.querySelector('.content-wrapper');
        if (!contentContainer) {
            contentContainer = document.createElement('div');
            contentContainer.className = 'content-wrapper';
            container.appendChild(contentContainer);
        }
        contentContainer.innerHTML = html;
    }

    /**
     * Отображение строки лога
     */
    renderLogRow(log) {
        const actionBadgeClass = this.getActionBadgeClass(log.action);
        const formattedDate = window.formatDate ? formatDate(log.timestamp) : log.timestamp;
        
        return `
            <tr>
                <td><small class="text-muted">#${log.id}</small></td>
                <td>
                    ${log.user ? 
                        `<span class="fw-medium">${log.user.username}</span>` : 
                        (log.user_id ? `<small class="text-muted">ID: ${log.user_id}</small>` : '<span class="text-muted">—</span>')
                    }
                </td>
                <td>
                    <span class="badge ${actionBadgeClass}">${log.action}</span>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 300px;" title="${log.details || ''}">
                        ${log.details || '<span class="text-muted">—</span>'}
                    </div>
                </td>
                <td>
                    <small class="text-muted font-monospace">${log.ip_address || '—'}</small>
                </td>
                <td>
                    <small class="text-muted">${formattedDate}</small>
                </td>
            </tr>
        `;
    }

    /**
     * Отображение пустого состояния
     */
    renderEmptyState() {
        return `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="text-muted">
                        <i class="bi bi-journal-x fs-1 mb-3 d-block"></i>
                        <h6>Логи не найдены</h6>
                        <p class="mb-0">Попробуйте изменить фильтры или очистить их</p>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Отображение активных фильтров
     */
    renderActiveFilters() {
        const activeFilters = Object.entries(this.currentFilters).filter(([key, value]) => value);
        
        if (activeFilters.length === 0) {
            return '';
        }

        const filterLabels = {
            action: 'Действие',
            user_id: 'Пользователь ID',
            date: 'Дата',
            month: 'Месяц',
            start_date: 'С даты',
            end_date: 'По дату'
        };

        const filterBadges = activeFilters.map(([key, value]) => {
            const label = filterLabels[key] || key;
            return `
                <span class="badge bg-info me-1">
                    ${label}: ${value}
                    <button type="button" class="btn-close btn-close-white ms-1" 
                            onclick="logsModule.removeFilter('${key}')" 
                            style="font-size: 0.7em;"></button>
                </span>
            `;
        }).join('');

        return `
            <div class="card-header bg-light border-top-0">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted me-2">Активные фильтры:</small>
                        ${filterBadges}
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" onclick="logsModule.clearAllFilters()">
                        <i class="bi bi-x-circle"></i> Очистить все
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Получение CSS класса для бейджа действия
     */
    getActionBadgeClass(action) {
        const actionClasses = {
            'LOGIN': 'bg-success',
            'LOGOUT': 'bg-secondary',
            'API_KEY_REGENERATED': 'bg-warning',
            'BALANCE_TOPUP': 'bg-info',
            'BALANCE_DEDUCT': 'bg-danger',
            'ACTIVATION_CREATED': 'bg-primary',
            'ACTIVATION_CANCELLED': 'bg-warning',
            'SMS_RECEIVED': 'bg-success',
            'ERROR': 'bg-danger',
            'SECURITY': 'bg-dark'
        };
        
        return actionClasses[action] || 'bg-secondary';
    }

    /**
     * Показать модальное окно фильтров
     */
    showFiltersModal() {
        // Создаем модальное окно для фильтров
        const modalHtml = `
            <div class="modal fade" id="logsFiltersModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-funnel"></i> Фильтры логов
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="logsFiltersForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Действие</label>
                                            <select class="form-select" name="action">
                                                <option value="">Все действия</option>
                                                <option value="LOGIN">Вход в систему</option>
                                                <option value="LOGOUT">Выход из системы</option>
                                                <option value="API_KEY_REGENERATED">Смена API ключа</option>
                                                <option value="BALANCE_TOPUP">Пополнение баланса</option>
                                                <option value="BALANCE_DEDUCT">Списание баланса</option>
                                                <option value="ACTIVATION_CREATED">Создание активации</option>
                                                <option value="ACTIVATION_CANCELLED">Отмена активации</option>
                                                <option value="SMS_RECEIVED">Получение SMS</option>
                                                <option value="ERROR">Ошибка</option>
                                                <option value="SECURITY">Безопасность</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ID пользователя</label>
                                            <input type="number" class="form-control" name="user_id" 
                                                   placeholder="Введите ID пользователя" min="1">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Конкретная дата</label>
                                            <input type="date" class="form-control" name="date">
                                            <div class="form-text">Логи за указанную дату</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Месяц</label>
                                            <input type="month" class="form-control" name="month">
                                            <div class="form-text">Логи за указанный месяц</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Начальная дата</label>
                                            <input type="date" class="form-control" name="start_date">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Конечная дата</label>
                                            <input type="date" class="form-control" name="end_date">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>Примечание:</strong> Если указана конкретная дата или месяц, 
                                    то период дат игнорируется.
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Отмена
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="logsModule.clearAllFilters()">
                                <i class="bi bi-x-circle"></i> Очистить фильтры
                            </button>
                            <button type="button" class="btn btn-primary" onclick="logsModule.applyFilters()">
                                <i class="bi bi-check-circle"></i> Применить фильтры
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем существующее модальное окно если есть
        const existingModal = document.getElementById('logsFiltersModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Заполняем форму текущими фильтрами
        this.populateFiltersForm();

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('logsFiltersModal'));
        modal.show();
    }

    /**
     * Заполнение формы фильтров текущими значениями
     */
    populateFiltersForm() {
        const form = document.getElementById('logsFiltersForm');
        if (!form) return;

        Object.entries(this.currentFilters).forEach(([key, value]) => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input && value) {
                input.value = value;
            }
        });
    }

    /**
     * Применение фильтров
     */
    async applyFilters() {
        const form = document.getElementById('logsFiltersForm');
        if (!form) return;

        const formData = new FormData(form);
        const newFilters = {};

        // Собираем только непустые значения
        for (const [key, value] of formData.entries()) {
            if (value.trim()) {
                newFilters[key] = value.trim();
            }
        }

        // Обновляем текущие фильтры
        this.currentFilters = newFilters;

        // Закрываем модальное окно
        const modal = bootstrap.Modal.getInstance(document.getElementById('logsFiltersModal'));
        if (modal) {
            modal.hide();
        }

        // Перезагружаем данные с новыми фильтрами
        await this.loadLogs();
    }

    /**
     * Удаление конкретного фильтра
     */
    async removeFilter(filterKey) {
        delete this.currentFilters[filterKey];
        await this.loadLogs();
    }

    /**
     * Очистка всех фильтров
     */
    async clearAllFilters() {
        this.currentFilters = {};

        // Закрываем модальное окно фильтров если открыто
        const modal = document.getElementById('logsFiltersModal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        }

        await this.loadLogs();
    }

    /**
     * Показать модальное окно очистки логов
     */
    showClearLogsModal() {
        const modalHtml = `
            <div class="modal fade" id="clearLogsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle"></i> Очистка логов
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill"></i>
                                <strong>ВНИМАНИЕ!</strong> Эта операция необратима!
                            </div>

                            <p>Выберите тип очистки логов:</p>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="clearType" id="clearAll" value="all" checked>
                                <label class="form-check-label fw-bold text-danger" for="clearAll">
                                    Очистить ВСЕ логи системы
                                </label>
                                <div class="form-text">Удалит все записи логов из базы данных</div>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="clearType" id="clearByPeriod" value="period">
                                <label class="form-check-label" for="clearByPeriod">
                                    Очистить логи за период
                                </label>
                                <div class="form-text">Удалит логи за указанный период времени</div>
                            </div>

                            <!-- Поля для очистки по периоду -->
                            <div id="periodFields" class="mt-3" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Начальная дата</label>
                                            <input type="date" class="form-control" id="clearStartDate">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Конечная дата</label>
                                            <input type="date" class="form-control" id="clearEndDate">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Действие (необязательно)</label>
                                    <select class="form-select" id="clearAction">
                                        <option value="">Все действия</option>
                                        <option value="LOGIN">Вход в систему</option>
                                        <option value="LOGOUT">Выход из системы</option>
                                        <option value="API_KEY_REGENERATED">Смена API ключа</option>
                                        <option value="BALANCE_TOPUP">Пополнение баланса</option>
                                        <option value="BALANCE_DEDUCT">Списание баланса</option>
                                        <option value="ACTIVATION_CREATED">Создание активации</option>
                                        <option value="ACTIVATION_CANCELLED">Отмена активации</option>
                                        <option value="SMS_RECEIVED">Получение SMS</option>
                                        <option value="ERROR">Ошибка</option>
                                        <option value="SECURITY">Безопасность</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" id="confirmClear" required>
                                <label class="form-check-label text-danger fw-bold" for="confirmClear">
                                    Я понимаю, что эта операция необратима
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Отмена
                            </button>
                            <button type="button" class="btn btn-danger" onclick="logsModule.executeClearLogs()" id="clearLogsBtn" disabled>
                                <i class="bi bi-trash"></i> Очистить логи
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем существующее модальное окно если есть
        const existingModal = document.getElementById('clearLogsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Настраиваем обработчики событий
        this.setupClearLogsModalEvents();

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('clearLogsModal'));
        modal.show();
    }

    /**
     * Настройка обработчиков событий для модального окна очистки логов
     */
    setupClearLogsModalEvents() {
        const clearTypeRadios = document.querySelectorAll('input[name="clearType"]');
        const periodFields = document.getElementById('periodFields');
        const confirmCheckbox = document.getElementById('confirmClear');
        const clearButton = document.getElementById('clearLogsBtn');

        // Обработчик изменения типа очистки
        clearTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                if (radio.value === 'period') {
                    periodFields.style.display = 'block';
                } else {
                    periodFields.style.display = 'none';
                }
                this.updateClearButtonState();
            });
        });

        // Обработчик подтверждения
        confirmCheckbox.addEventListener('change', () => {
            this.updateClearButtonState();
        });

        // Обработчики для полей периода
        const periodInputs = ['clearStartDate', 'clearEndDate', 'clearAction'];
        periodInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('change', () => {
                    this.updateClearButtonState();
                });
            }
        });
    }

    /**
     * Обновление состояния кнопки очистки логов
     */
    updateClearButtonState() {
        const clearButton = document.getElementById('clearLogsBtn');
        const confirmCheckbox = document.getElementById('confirmClear');
        const clearType = document.querySelector('input[name="clearType"]:checked')?.value;

        if (!clearButton || !confirmCheckbox) return;

        let isValid = confirmCheckbox.checked;

        // Дополнительная валидация для очистки по периоду
        if (clearType === 'period' && isValid) {
            const startDate = document.getElementById('clearStartDate')?.value;
            const endDate = document.getElementById('clearEndDate')?.value;
            const action = document.getElementById('clearAction')?.value;

            // Требуем хотя бы один параметр для очистки по периоду
            isValid = startDate || endDate || action;
        }

        clearButton.disabled = !isValid;
    }

    /**
     * Выполнение очистки логов
     */
    async executeClearLogs() {
        const clearType = document.querySelector('input[name="clearType"]:checked')?.value;
        const clearButton = document.getElementById('clearLogsBtn');

        if (!clearType || !clearButton) return;

        // Блокируем кнопку и показываем индикатор загрузки
        clearButton.disabled = true;
        const originalText = clearButton.innerHTML;
        clearButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Очистка...';

        try {
            let response;

            if (clearType === 'all') {
                // Очистка всех логов
                response = await this.apiRequest('/api/admin/logs', {
                    method: 'DELETE'
                });
            } else if (clearType === 'period') {
                // Очистка логов по периоду
                const startDate = document.getElementById('clearStartDate')?.value;
                const endDate = document.getElementById('clearEndDate')?.value;
                const action = document.getElementById('clearAction')?.value;

                const params = new URLSearchParams();
                if (startDate) params.append('start_date', startDate);
                if (endDate) params.append('end_date', endDate);
                if (action) params.append('action', action);

                response = await this.apiRequest(`/api/admin/logs/by-period?${params}`, {
                    method: 'DELETE'
                });
            }

            if (response && response.success) {
                this.showSuccess(`${response.message}`);

                // Закрываем модальное окно
                const modal = bootstrap.Modal.getInstance(document.getElementById('clearLogsModal'));
                if (modal) {
                    modal.hide();
                }

                // Перезагружаем логи
                await this.loadLogs();
            } else {
                throw new Error(response?.message || 'Неизвестная ошибка при очистке логов');
            }

        } catch (error) {
            console.error('Ошибка очистки логов:', error);
            this.showError(`Ошибка очистки логов: ${error.message}`);
        } finally {
            // Восстанавливаем кнопку
            clearButton.innerHTML = originalText;
            this.updateClearButtonState();
        }
    }

    /**
     * API запрос (используем глобальный apiClient)
     */
    async apiRequest(url, options = {}) {
        if (window.apiClient) {
            return await apiClient.request(url, options);
        } else {
            throw new Error('API клиент не инициализирован');
        }
    }

    /**
     * Показать ошибку (используем глобальный uiManager)
     */
    showError(message) {
        if (window.uiManager) {
            uiManager.showError(message);
        } else {
            console.error('UI Manager не инициализирован:', message);
            alert(`Ошибка: ${message}`);
        }
    }

    /**
     * Показать успешное сообщение (используем глобальный uiManager)
     */
    showSuccess(message) {
        if (window.uiManager) {
            uiManager.showSuccess(message);
        } else {
            console.log('Успех:', message);
            alert(`Успех: ${message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля логов
const logsModule = new LogsModule();

// Экспортируем в глобальную область видимости
window.logsModule = logsModule;
