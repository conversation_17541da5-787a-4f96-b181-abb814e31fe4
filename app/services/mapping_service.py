"""
Сервис для маппинга кодов между SMSActivate и провайдерами
"""
from __future__ import annotations

import os
from typing import Dict, Optional, Tuple


class MappingService:
    """Сервис для преобразования кодов между форматами"""

    def __init__(self):
        self._country_mappings: Dict[int, Tuple[str, str, str]] = {}
        self._service_mappings: Dict[str, Tuple[str, str]] = {}
        self._firefox_to_sms_countries: Dict[str, int] = {}
        self._firefox_to_sms_services: Dict[str, str] = {}
        self._load_mappings()

    def _load_mappings(self):
        """Загрузка маппингов из файла mappings.txt"""
        mappings_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "mappings.txt"
        )

        if not os.path.exists(mappings_file):
            raise FileNotFoundError(f"Файл маппингов не найден: {mappings_file}")

        with open(mappings_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        current_section = None

        for line in lines:
            line = line.strip()

            # Пропускаем комментарии и пустые строки
            if not line or line.startswith("#"):
                if "Страны" in line:
                    current_section = "countries"
                elif "Сервисы" in line:
                    current_section = "services"
                continue

            # Парсим строки данных
            parts = line.split("|")
            if len(parts) < 3:
                continue

            if current_section == "countries" and len(parts) >= 4:
                # Формат: sms_activate_country_id|firefox_country_code|country_prefix|country_name
                sms_code = int(parts[0])
                firefox_code = parts[1]
                calling_code = parts[2]
                country_name = parts[3]

                self._country_mappings[sms_code] = (
                    firefox_code,
                    calling_code,
                    country_name,
                )
                self._firefox_to_sms_countries[firefox_code] = sms_code

            elif current_section == "services" and len(parts) >= 3:
                # Формат: sms_activate_service_id|firefox_service_id|service_name
                sms_code = parts[0]
                firefox_iid = parts[1]
                service_name = parts[2]

                self._service_mappings[sms_code] = (firefox_iid, service_name)
                self._firefox_to_sms_services[firefox_iid] = sms_code

    # Методы для работы со странами
    def get_firefox_country_code(self, sms_country_code: int) -> Optional[str]:
        """Получить код страны Firefox по коду SMSActivate"""
        mapping = self._country_mappings.get(sms_country_code)
        return mapping[0] if mapping else None

    def get_calling_code(self, sms_country_code: int) -> Optional[str]:
        """Получить телефонный код страны по коду SMSActivate"""
        mapping = self._country_mappings.get(sms_country_code)
        return mapping[1] if mapping else None

    def get_country_name(self, sms_country_code: int) -> Optional[str]:
        """Получить название страны по коду SMSActivate"""
        mapping = self._country_mappings.get(sms_country_code)
        return mapping[2] if mapping else None

    def get_sms_country_code(self, firefox_country_code: str) -> Optional[int]:
        """Получить код страны SMSActivate по коду Firefox"""
        return self._firefox_to_sms_countries.get(firefox_country_code)

    # Методы для работы с сервисами
    def get_firefox_service_iid(self, sms_service_code: str) -> Optional[str]:
        """Получить IID сервиса Firefox по коду SMSActivate"""
        mapping = self._service_mappings.get(sms_service_code)
        return mapping[0] if mapping else None

    def get_service_name(self, sms_service_code: str) -> Optional[str]:
        """Получить название сервиса по коду SMSActivate"""
        mapping = self._service_mappings.get(sms_service_code)
        return mapping[1] if mapping else None

    def get_sms_service_code(self, firefox_service_iid: str) -> Optional[str]:
        """Получить код сервиса SMSActivate по IID Firefox"""
        return self._firefox_to_sms_services.get(firefox_service_iid)

    # Методы для получения всех маппингов
    def get_all_countries(self) -> Dict[int, Tuple[str, str, str]]:
        """Получить все маппинги стран"""
        return self._country_mappings.copy()

    def get_all_services(self) -> Dict[str, Tuple[str, str]]:
        """Получить все маппинги сервисов"""
        return self._service_mappings.copy()

    def is_country_supported(self, sms_country_code: int) -> bool:
        """Проверить, поддерживается ли страна"""
        return sms_country_code in self._country_mappings

    def is_service_supported(self, sms_service_code: str) -> bool:
        """Проверить, поддерживается ли сервис"""
        return sms_service_code in self._service_mappings


# Глобальный экземпляр сервиса маппинга
mapping_service = MappingService()
