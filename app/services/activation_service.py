"""
Сервис для работы с активациями номеров
"""
from __future__ import annotations

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, Optional

from sqlalchemy import and_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.config import settings as app_settings
from app.core.logger import critical, debug, info
from app.models import Activation, Country, Price, Service, Setting, User
from app.providers.dynamic_manager import dynamic_manager
from app.schemas import ActivationCreate, ActivationUpdate

from .balance_service import BalanceService
from .base import BaseService
from .exceptions import (
    ActivationNotFoundError,
    InsufficientFundsError,
    ServiceError,
    UserNotFoundError,
)


class ActivationService(BaseService):
    """Сервис для работы с активациями"""

    async def create_activation(
        self,
        user_id: int,
        service_code: str,
        country_code: Optional[int] = None,
        ip_address: str = None,
        operator: Optional[str] = None,
        max_price: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        Создание новой активации номера

        Логика:
        1. Проверяем пользователя и его баланс
        2. Получаем цену услуги
        3. Проверяем достаточность средств (НЕ резервируем!)
        4. Заказываем номер у провайдера
        5. Создаем активацию в БД

        Args:
            user_id: ID пользователя
            service_code: Код сервиса (tg, wa, vk)
            country_code: Код страны (0=Россия)
            ip_address: IP-адрес клиента (только для логов)
            operator: Оператор связи (опционально, поддерживается для SMSLive)
            max_price: Максимальная цена (опционально, поддерживается для SMSLive)

        Returns:
            Результат создания активации
        """
        # Получаем пользователя
        user = await self._get_user_by_id(user_id)
        if not user:
            return {"success": False, "error": "Пользователь не найден"}

        # Получаем сервис
        service = await self._get_service_by_code(service_code)
        if not service or not service.is_active:
            return {"success": False, "error": "BAD_SERVICE"}

        # Получаем страну
        country = None
        if country_code is not None:
            country = await self._get_country_by_code(country_code)
            if not country or not country.is_active:
                return {"success": False, "error": "BAD_COUNTRY"}

        # Получаем цену
        price = await self._get_price(service.id, country.id if country else None)
        if not price:
            return {"success": False, "error": "NO_PRICE"}

        # Проверяем баланс (НЕ резервируем, только проверяем!)
        if user.balance < price.price:
            return {"success": False, "error": "NO_BALANCE"}

        # Пытаемся получить номер от провайдеров через новый динамический менеджер
        kwargs = {}
        if max_price is not None:
            kwargs["maxPrice"] = max_price
        if operator is not None:
            kwargs["operator"] = operator

        result = await dynamic_manager.get_number(
            service_code=service_code, country_code=country_code or 0, **kwargs
        )

        if not result.get("success"):
            return {"success": False, "error": result.get("error", "NO_NUMBERS")}

        # Создаем активацию в БД
        activation = Activation(
            user_id=user_id,
            provider=result["provider"],
            provider_ref=result["activation_id"],
            country_id=country.id if country else None,
            service_id=service.id,
            phone_number=result["phone_number"],
            cost=price.price,
            status="WAIT_SMS",
            charged=False,  # Средства еще НЕ списаны!
        )

        self.db.add(activation)
        await self.db.commit()
        await self.db.refresh(activation)

        # Логируем создание активации только если ip_address передан
        operator_info = f", оператор: {operator}" if operator else ""
        await self._log_action(
            "REQUEST_NUMBER",
            f"Заказан номер {result['phone_number']} для сервиса {service_code}{operator_info}",
            user_id,
            ip_address,
        )

        # Убираем плюс из номера телефона
        clean_phone = result["phone_number"].lstrip("+")

        return {
            "success": True,
            "activation_id": str(activation.id),
            "phone_number": clean_phone,
            "provider": result["provider"],
        }

    async def get_activation_status(
        self, activation_id: str, user_id: int, ip_address: str = None
    ) -> Dict[str, Any]:
        """
        Получение статуса активации

        Логика:
        1. Получаем активацию из БД
        2. Если SMS уже получен - возвращаем код
        3. Если отменена - возвращаем статус
        4. Проверяем SMS у провайдера
        5. Если SMS получен - списываем средства

        Args:
            activation_id: ID активации
            user_id: ID пользователя
            ip_address: IP-адрес клиента (только для логов)
        Returns:
            Статус активации
        """
        try:
            activation_int_id = int(activation_id)
        except ValueError:
            return {"success": False, "error": "BAD_ACTIVATION_ID"}

        # Получаем активацию
        activation = await self._get_activation(activation_int_id, user_id)
        if not activation:
            return {"success": False, "error": "NO_ACTIVATION"}

        # Если SMS уже получен, возвращаем код
        if activation.status == "SMS_RECEIVED" and activation.sms_code:
            return {
                "success": True,
                "status": "STATUS_OK",
                "sms_code": activation.sms_code,
            }

        # Если активация отменена
        if activation.status in ["CANCELED", "TIMEOUT"]:
            return {"success": True, "status": "STATUS_CANCEL"}

        # Проверяем SMS у провайдера ТОЛЬКО если статус WAIT_SMS
        if activation.status == "WAIT_SMS":
            # ip_address используется только для логирования
            await self._check_sms_from_provider(activation, ip_address)
            await self.db.commit()
            if activation.status == "SMS_RECEIVED" and activation.sms_code:
                return {
                    "success": True,
                    "status": "STATUS_OK",
                    "sms_code": activation.sms_code,
                }
            elif activation.status == "CANCELED":
                return {"success": True, "status": "STATUS_CANCEL"}

        return {"success": True, "status": "STATUS_WAIT_CODE"}

    async def cancel_activation(
        self, activation_id: str, user_id: int, ip_address: str = None
    ) -> Dict[str, Any]:
        """
        Отмена активации пользователем

        Логика:
        1. Проверяем, что активация существует и принадлежит пользователю
        2. Проверяем, что активация еще не завершена
        3. Отменяем у провайдера
        4. Обновляем статус в БД

        Args:
            activation_id: ID активации
            user_id: ID пользователя
            ip_address: IP-адрес клиента (только для логов)
        Returns:
            Результат отмены
        """
        try:
            activation_int_id = int(activation_id)
        except ValueError:
            return {"success": False, "error": "BAD_ACTIVATION_ID"}

        activation = await self._get_activation(activation_int_id, user_id)
        if not activation:
            return {"success": False, "error": "NO_ACTIVATION"}

        if activation.status != "WAIT_SMS":
            return {"success": False, "error": "ACTIVATION_ALREADY_PROCESSED"}

        # ip_address используется только для логирования
        await self._cancel_at_provider(activation, ip_address)

        # Обновляем статус
        activation.status = "CANCELED"
        activation.completed_at = datetime.utcnow()

        await self.db.commit()

        return {"success": True}

    async def request_retry(
        self, activation_id: str, user_id: int, ip_address: str = None
    ) -> Dict[str, Any]:
        """
        Запрос повторного SMS кода

        Логика:
        1. Получаем активацию из БД
        2. Если активация не готова для запроса - возвращаем ошибку
        3. Запрашиваем повторный код у провайдера через динамический менеджер
        4. Если код получен - обновляем статус активации
        5. Если код не получен - возвращаем ошибку

        Args:
            activation_id: ID активации
            user_id: ID пользователя
            ip_address: IP-адрес клиента (только для логов)
        Returns:
            Результат запроса
        """
        try:
            activation_int_id = int(activation_id)
        except ValueError:
            return {"success": False, "error": "BAD_ACTIVATION_ID"}

        activation = await self._get_activation(activation_int_id, user_id)
        if not activation:
            return {"success": False, "error": "NO_ACTIVATION"}

        if activation.status not in ["WAIT_SMS", "SMS_RECEIVED"]:
            return {"success": False, "error": "ACTIVATION_NOT_READY"}

        # Запрашиваем повторный код у провайдера через новый динамический менеджер
        try:
            # Используем новый метод request_retry из динамического менеджера
            result = await dynamic_manager.request_retry(
                activation.provider_ref, activation.provider
            )

            if result.get("success"):
                # Сбрасываем статус на ожидание
                activation.status = "WAIT_SMS"
                activation.sms_code = None

                await self.db.commit()

                await self._log_action(
                    "RETRY_REQUEST",
                    f"Запрошен повторный код для активации {activation.id}",
                    user_id,
                    ip_address,
                )

                return {"success": True}
            else:
                return {
                    "success": False,
                    "error": result.get("error", "RETRY_FAILED"),
                }

        except Exception as e:
            # Логируем ошибку только если ip_address передан
            await self._log_action(
                "ERROR",
                f"Ошибка при запросе повторного кода: {str(e)}",
                user_id,
                ip_address,
            )
            return {"success": False, "error": "RETRY_ERROR"}

    # Вспомогательные методы
    async def _get_user_by_id(self, user_id: int) -> Optional[User]:
        """Получение пользователя по ID"""
        result = await self.db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()

    async def _deduct_balance_direct(
        self, user_id: int, amount: Decimal, comment: str = None
    ) -> bool:
        """
        Прямое списание с баланса пользователя без создания новой транзакции
        Используется внутри уже существующей транзакции
        """
        from app.models import Transaction, TransactionType
        from app.utils.validators import validate_positive_amount

        # Валидация входных данных
        amount = validate_positive_amount(amount, "Сумма списания")

        # Получаем пользователя с блокировкой строки
        user = await self.db.execute(
            select(User).where(User.id == user_id).with_for_update()
        )
        user = user.scalar_one_or_none()

        if not user:
            raise Exception(f"Пользователь с ID {user_id} не найден")

        # Проверяем наличие средств
        if user.balance < amount:
            raise Exception(
                f"Недостаточно средств. Баланс: {user.balance}, требуется: {amount}"
            )

        # Атомарное обновление баланса на уровне БД
        result = await self.db.execute(
            update(User).where(User.id == user_id).values(balance=User.balance - amount)
        )

        # Проверяем, что обновление прошло успешно
        if result.rowcount == 0:
            raise Exception("Не удалось списать средства")

        # Создаем транзакцию списания
        transaction = Transaction(
            user_id=user_id,
            amount=-amount,  # Отрицательная сумма для списания
            type=TransactionType.CHARGE,
            comment=comment or f"Списание {amount}",
        )

        self.db.add(transaction)
        return True

    async def _get_service_by_code(self, code: str) -> Optional[Service]:
        """Получение сервиса по коду"""
        result = await self.db.execute(select(Service).where(Service.code == code))
        return result.scalar_one_or_none()

    async def _get_country_by_code(self, code: int) -> Optional[Country]:
        """Получение страны по коду"""
        result = await self.db.execute(select(Country).where(Country.code == code))
        return result.scalar_one_or_none()

    async def _get_price(
        self, service_id: int, country_id: Optional[int]
    ) -> Optional[Price]:
        """Получение цены"""
        result = await self.db.execute(
            select(Price).where(
                and_(Price.service_id == service_id, Price.country_id == country_id)
            )
        )
        return result.scalar_one_or_none()

    async def _get_activation(
        self, activation_id: int, user_id: int
    ) -> Optional[Activation]:
        """Получение активации"""
        result = await self.db.execute(
            select(Activation)
            .options(selectinload(Activation.user))
            .where(and_(Activation.id == activation_id, Activation.user_id == user_id))
        )
        return result.scalar_one_or_none()

    async def _check_sms_from_provider(
        self, activation: Activation, ip_address: str = None
    ):
        """
        Проверка SMS у провайдера

        ВАЖНО: Списание происходит ТОЛЬКО здесь, ТОЛЬКО при получении SMS!
        """
        try:
            # Проверяем SMS у провайдера через новый динамический менеджер
            result = await dynamic_manager.get_status(
                activation.provider_ref, activation.provider
            )

            # Проверяем результат получения SMS
            if result.get("success"):
                if result.get("status") == "completed" and result.get("sms_code"):
                    # SMS получен - обновляем активацию
                    activation.status = "SMS_RECEIVED"
                    activation.sms_code = result["sms_code"]
                    activation.completed_at = datetime.utcnow()

                    # СПИСЫВАЕМ СРЕДСТВА ТОЛЬКО СЕЙЧАС!
                    if not activation.charged:
                        try:
                            # Проверяем баланс и списываем средства напрямую
                            user = await self._get_user_by_id(activation.user_id)
                            if user and user.balance >= activation.cost:
                                # Списываем средства напрямую без создания новой транзакции
                                await self._deduct_balance_direct(
                                    user_id=activation.user_id,
                                    amount=activation.cost,
                                    comment=f"Списание за получение SMS для активации {activation.id}",
                                )
                                activation.charged = True

                                await self._log_action(
                                    "SMS_RECEIVED",
                                    f"Получен SMS код для активации {activation.id}, средства списаны",
                                    activation.user_id,
                                    ip_address,
                                )
                            else:
                                # Недостаточно средств - отменяем активацию
                                activation.status = "CANCELED"
                                await self._log_action(
                                    "ERROR",
                                    f"Недостаточно средств для списания при получении SMS для активации {activation.id}",
                                    activation.user_id,
                                    ip_address,
                                )
                        except Exception as e:
                            await self._log_action(
                                "ERROR",
                                f"Ошибка списания средств при получении SMS: {str(e)}",
                                activation.user_id,
                                ip_address,
                            )

                elif result.get("status") == "cancelled":
                    # Номер отменен провайдером
                    activation.status = "CANCELED"
                    activation.completed_at = datetime.utcnow()

                    await self._log_action(
                        "PROVIDER_CANCEL",
                        f"Провайдер отменил активацию {activation.id}",
                        activation.user_id,
                        ip_address,
                    )
                # Если waiting_for_sms - просто ждем дальше

                # НЕ делаем commit здесь - это должен делать вызывающий код

        except Exception as e:
            # Логируем ошибку, но не прерываем процесс
            await self._log_action(
                "ERROR",
                f"Ошибка при проверке SMS у провайдера {activation.provider}: {str(e)}",
                activation.user_id,
                ip_address,
            )

    async def _cancel_at_provider(self, activation: Activation, ip_address: str = None):
        """Отмена активации у провайдера"""
        try:
            # Отменяем у провайдера через новый динамический менеджер
            result = await dynamic_manager.cancel_activation(
                activation.provider_ref, activation.provider
            )

            if not result.get("success"):
                await self._log_action(
                    "ERROR",
                    f"Провайдер не смог отменить активацию: {result.get('error')}",
                    activation.user_id,
                    ip_address,
                )

        except Exception as e:
            # Логируем ошибку, но не прерываем процесс
            await self._log_action(
                "ERROR",
                f"Ошибка при отмене у провайдера {activation.provider}: {str(e)}",
                activation.user_id,
                ip_address,
            )
