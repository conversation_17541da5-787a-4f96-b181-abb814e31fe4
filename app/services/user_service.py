"""
Сервис для работы с пользователями
"""
from __future__ import annotations

from decimal import Decimal
from typing import Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import Transaction, TransactionType, User
from app.models.log import Log
from app.schemas import UserCreate, UserUpdate

from .balance_service import BalanceService
from .base import BaseService
from .exceptions import InsufficientFundsError, ServiceError, UserNotFoundError


class UserService(BaseService):
    """Сервис для работы с пользователями"""

    async def get_user_by_api_key(self, api_key: str) -> Optional[User]:
        """Получение пользователя по API ключу"""
        from datetime import datetime

        result = await self.db.execute(
            select(User).where(and_(User.api_key == api_key, User.is_active == True))
        )
        user = result.scalar_one_or_none()

        # Обновляем время последнего входа при каждом обращении к API
        if user:
            user.last_login = datetime.now()
            await self.db.commit()

        return user

    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Получение пользователя по ID"""
        result = await self.db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()

    async def create_user(self, user_data: UserCreate, ip_address: str = None) -> User:
        """Создание нового пользователя"""
        # Генерируем уникальный API ключ
        api_key = self._generate_api_key()

        user = User(
            username=user_data.username,
            email=user_data.email,
            api_key=api_key,
            role=user_data.role,
            is_active=user_data.is_active,
            balance=getattr(
                user_data, "balance", Decimal("0")
            ),  # Устанавливаем начальный баланс
        )

        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)

        # Если был установлен начальный баланс больше 0, создаем транзакцию
        if user.balance > 0:
            transaction = Transaction(
                user_id=user.id,
                amount=user.balance,
                type=TransactionType.INITIAL_BALANCE,
                comment="Начальный баланс при создании пользователя",
            )
            self.db.add(transaction)

        # Логируем создание пользователя
        await self._log_action(
            "USER_CREATED",
            f"Создан пользователь {user.username} с балансом {user.balance}",
            user.id,
            ip_address,
        )

        await self.db.commit()
        return user

    async def update_user(
        self, user_id: int, user_data: UserUpdate, ip_address: str = None
    ) -> Optional[User]:
        """Обновление пользователя"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None

        # Сохраняем старый API ключ для очистки кеша
        old_api_key = user.api_key

        update_data = user_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)

        await self.db.commit()
        await self.db.refresh(user)

        # Очищаем кеш авторизации если это администратор
        if user.role == "admin":
            from app.core.security import clear_admin_auth_cache

            clear_admin_auth_cache(old_api_key)
            # Если API ключ изменился, очищаем и новый
            if old_api_key != user.api_key:
                clear_admin_auth_cache(user.api_key)

        # Логируем обновление
        await self._log_action(
            "USER_UPDATED",
            f"Обновлен пользователь {user.username}",
            user.id,
            ip_address,
        )

        return user

    async def topup_user_balance(
        self, user_id: int, amount: Decimal, comment: str = None, ip_address: str = None
    ) -> bool:
        """
        Пополнение баланса пользователя (только для административных действий)

        Args:
            user_id: ID пользователя
            amount: Сумма пополнения
            comment: Комментарий к операции
            ip_address: IP-адрес пользователя (только для логов админки)

        Returns:
            True если баланс пополнен
        """
        balance_service = BalanceService(self.db)
        # Передаем ip_address только если это административное действие (через админку)
        # Для обычных пользователей ip_address не нужен и не логируется
        if ip_address:
            return await balance_service.topup_balance(
                user_id, amount, comment, ip_address
            )
        else:
            return await balance_service.topup_balance(user_id, amount, comment)

    async def deduct_user_balance(
        self, user_id: int, amount: Decimal, comment: str = None, ip_address: str = None
    ) -> bool:
        """
        Списание с баланса пользователя (только для административных действий)

        Args:
            user_id: ID пользователя
            amount: Сумма списания
            comment: Комментарий к операции
            ip_address: IP-адрес пользователя (только для логов админки)

        Returns:
            True если средства списаны
        """
        balance_service = BalanceService(self.db)
        # Передаем ip_address только если это административное действие (через админку)
        # Для обычных пользователей ip_address не нужен и не логируется
        if ip_address:
            return await balance_service.deduct_balance(
                user_id, amount, comment, ip_address
            )
        else:
            return await balance_service.deduct_balance(user_id, amount, comment)

    async def get_user_balance_info(self, user_id: int) -> dict:
        """
        Получение информации о балансе пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Словарь с информацией о балансе
        """
        balance_service = BalanceService(self.db)
        return await balance_service.get_user_balance(user_id)
