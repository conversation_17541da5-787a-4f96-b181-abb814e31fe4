"""
Базовые исключения для сервисов
"""


from __future__ import annotations


class ServiceError(Exception):
    """Исключение для ошибок бизнес-логики"""

    pass


class UserNotFoundError(ServiceError):
    """Пользователь не найден"""

    pass


class InsufficientFundsError(ServiceError):
    """Недостаточно средств на балансе"""

    pass


class ActivationNotFoundError(ServiceError):
    """Активация не найдена"""

    pass


class ProviderError(ServiceError):
    """Ошибка провайдера"""

    pass
