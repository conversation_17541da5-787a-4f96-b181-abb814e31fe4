"""
Сервис для управления балансом пользователей
"""
from __future__ import annotations

from decimal import Decimal
from typing import Optional

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import Transaction, TransactionType, User
from app.models.log import Log
from app.utils.validators import validate_positive_amount

from .base import BaseService
from .exceptions import InsufficientFundsError, ServiceError, UserNotFoundError


class BalanceService(BaseService):
    """Сервис для управления балансом пользователей"""

    async def topup_balance(
        self, user_id: int, amount: Decimal, comment: str = None, ip_address: str = None
    ) -> bool:
        """
        Пополнение баланса пользователя

        Args:
            user_id: ID пользователя
            amount: Сумма пополнения
            comment: Комментарий к операции
            ip_address: IP-адрес пользователя (только для логов админки)

        Returns:
            True если баланс пополнен
        """
        # Валидация входных данных
        amount = validate_positive_amount(amount, "Сумма пополнения")

        async with self.db.begin():
            # Получаем пользователя
            user = await self._get_user_with_lock(user_id, for_update=True)

            # Обновляем баланс
            result = await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(balance=User.balance + amount)
            )

            if result.rowcount == 0:
                raise ServiceError("Не удалось пополнить баланс")

            # Создаем транзакцию пополнения
            transaction = Transaction(
                user_id=user_id,
                amount=amount,
                type=TransactionType.TOPUP,
                comment=comment or f"Пополнение баланса на {amount}",
            )

            # Создаем лог только если ip_address передан (админка)
            if ip_address:
                log_entry = Log(
                    user_id=user_id,
                    action="BALANCE_TOPUP",
                    details=f"Пополнение баланса на {amount}. Новый баланс: {user.balance + amount}",
                    ip_address=ip_address,
                )
                self.db.add(log_entry)

            self.db.add(transaction)

            return True

    async def deduct_balance(
        self, user_id: int, amount: Decimal, comment: str = None, ip_address: str = None
    ) -> bool:
        """
        Списание с баланса пользователя

        Args:
            user_id: ID пользователя
            amount: Сумма для списания
            comment: Комментарий к операции
            ip_address: IP-адрес пользователя (только для логов админки)

        Returns:
            True если средства списаны

        Raises:
            InsufficientFundsError: Если недостаточно средств
        """
        # Валидация входных данных
        amount = validate_positive_amount(amount, "Сумма списания")

        async with self.db.begin():
            # Получаем пользователя с блокировкой строки
            user = await self._get_user_with_lock(user_id, for_update=True)

            # Проверяем наличие средств
            if user.balance < amount:
                raise InsufficientFundsError(
                    f"Недостаточно средств. Баланс: {user.balance}, требуется: {amount}"
                )

            # Атомарное обновление баланса на уровне БД
            result = await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(balance=User.balance - amount)
            )

            # Проверяем, что обновление прошло успешно
            if result.rowcount == 0:
                raise InsufficientFundsError("Не удалось списать средства")

            # Создаем транзакцию списания
            transaction = Transaction(
                user_id=user_id,
                amount=-amount,  # Отрицательная сумма для списания
                type=TransactionType.CHARGE,
                comment=comment or f"Списание {amount}",
            )

            # Создаем лог только если ip_address передан (админка)
            if ip_address:
                log_entry = Log(
                    user_id=user_id,
                    action="BALANCE_DEDUCT",
                    details=f"Списание {amount}. Новый баланс: {user.balance - amount}",
                    ip_address=ip_address,
                )
                self.db.add(log_entry)

            self.db.add(transaction)

            return True

    async def _get_user_with_lock(self, user_id: int, for_update: bool = False) -> User:
        """
        Получение пользователя по ID с опциональной пессимистической блокировкой

        Args:
            user_id: ID пользователя
            for_update: Использовать SELECT FOR UPDATE для блокировки строки

        Returns:
            Объект пользователя

        Raises:
            UserNotFoundError: Если пользователь не найден
        """
        query = select(User).where(User.id == user_id)

        if for_update:
            # Используем пессимистическую блокировку
            query = query.with_for_update()

        result = await self.db.execute(query)
        user = result.scalar_one_or_none()

        if not user:
            raise UserNotFoundError(f"Пользователь с ID {user_id} не найден")

        return user
