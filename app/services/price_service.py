"""
Сервис для работы с ценами
"""
from __future__ import annotations

from typing import Any, Dict, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import Country, Price, Service


class PriceService:
    """Сервис для работы с ценами"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_prices(self, country_code: Optional[int] = None) -> Dict[str, Any]:
        """
        Получение цен и доступности номеров

        Args:
            country_code: Код страны (опционально)

        Returns:
            Словарь с ценами в формате SMSActivate
        """
        query = (
            select(Price, Country, Service)
            .join(Country, Price.country_id == Country.id)
            .join(Service, Price.service_id == Service.id)
            .where(and_(Country.is_active == True, Service.is_active == True))
        )

        if country_code is not None:
            query = query.where(Country.code == country_code)

        result = await self.db.execute(query)
        rows = result.all()

        # Формируем ответ в формате SMSActivate
        prices = {}
        for price, country, service in rows:
            country_key = str(country.code)
            if country_key not in prices:
                prices[country_key] = {}

            prices[country_key][service.code] = {
                "price": float(price.price),
                "count": price.available or 0,
            }

        return {"status": "success", "prices": prices}

    async def get_numbers_status(
        self, country_code: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Получение количества доступных номеров

        Args:
            country_code: Код страны (опционально)

        Returns:
            Словарь с количеством номеров в формате {"service_code": "count"}
        """
        query = (
            select(Price, Country, Service)
            .join(Country, Price.country_id == Country.id)
            .join(Service, Price.service_id == Service.id)
            .where(and_(Country.is_active == True, Service.is_active == True))
        )

        if country_code is not None:
            query = query.where(Country.code == country_code)

        result = await self.db.execute(query)
        rows = result.all()

        # Формируем ответ в формате {"service_code": "count"}
        numbers_status = {}
        for price, country, service in rows:
            # Суммируем доступные номера по всем странам для каждого сервиса
            if service.code not in numbers_status:
                numbers_status[service.code] = 0
            numbers_status[service.code] += price.available or 0

        # Преобразуем числа в строки как требует API
        return {service: str(count) for service, count in numbers_status.items()}
