"""
Базовый сервис для всех бизнес-сервисов
"""
from __future__ import annotations

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logger import critical, get_logger
from app.models import Log

logger = get_logger(__name__)


class BaseService:
    """Базовый класс для всех сервисов"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = logger

    async def _log_action(
        self,
        action: str,
        description: str = None,
        user_id: int = None,
        ip_address: str = None,
    ):
        """
        Логирование действий в базу данных

        Args:
            action: Тип действия
            description: Описание действия
            user_id: ID пользователя (если применимо)
            ip_address: IP-адрес пользователя (если применимо)
        """
        try:
            log_entry = Log(
                action=action,
                details=description,
                user_id=user_id,
                ip_address=ip_address,
            )
            self.db.add(log_entry)
            # Не делаем commit здесь, так как это может быть частью большей транзакции
        except Exception as e:
            critical(f"Ошибка при логировании: {e}, действие: {action}")

    def _generate_api_key(self) -> str:
        """Генерация API ключа"""
        import secrets

        return secrets.token_hex(32)
