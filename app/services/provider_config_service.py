"""
Сервис для управления конфигурацией SMS провайдеров
"""
from __future__ import annotations

from datetime import datetime
import time
from typing import Any, Dict, List, Optional

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_database_session
from app.core.logger import debug, error, info
from app.models.sms_provider import SMSProvider
from app.providers.dynamic_factory import DynamicProviderFactory, ProviderError
from app.providers.dynamic_manager import dynamic_manager
from app.schemas.sms_provider import (
    APIFormat,
    ProviderCreateSchema,
    ProviderResponseSchema,
    ProviderStatus,
    ProviderTestResponseSchema,
    ProviderUpdateSchema,
)
from app.utils.encryption import mask_sensitive_data


class ProviderConfigService:
    """
    Сервис для управления конфигурацией SMS провайдеров
    Включает CRUD операции, тестирование и управление состоянием
    """

    def __init__(self, db: AsyncSession):
        """
        Инициализация сервиса

        Args:
            db: Сессия базы данных
        """
        self.db = db
        self.factory = DynamicProviderFactory()

    async def create_provider(
        self, config: ProviderCreateSchema, created_by_user_id: Optional[int] = None
    ) -> SMSProvider:
        """
        Создать новый провайдер

        Args:
            config: Схема создания провайдера
            created_by_user_id: ID пользователя создавшего провайдера

        Returns:
            Созданный провайдер

        Raises:
            ValueError: При ошибке валидации или создания
        """
        # Проверяем уникальность имени
        existing = await self.db.execute(
            select(SMSProvider).where(SMSProvider.name == config.name)
        )
        if existing.scalar_one_or_none():
            raise ValueError(f"Провайдер с именем '{config.name}' уже существует")

        # Определяем тип провайдера
        provider_type = (
            "firefox" if config.api_format == APIFormat.FIREFOX else "smsactivate"
        )

        # Сохраняем учетные данные БЕЗ шифрования
        api_key = None
        password = None

        if config.api_format == APIFormat.SMSACTIVATE and config.api_key:
            api_key = config.api_key  # Сохраняем в открытом виде
            debug(f"API ключ сохранен для провайдера {config.name}")

        if config.api_format == APIFormat.FIREFOX and config.password:
            password = config.password  # Сохраняем в открытом виде
            debug(f"Пароль сохранен для провайдера {config.name}")

        # Создаем запись в БД
        db_provider = SMSProvider(
            name=config.name,
            display_name=config.display_name,
            api_url=config.api_url,
            api_key=api_key,
            username=config.username,
            password=password,
            provider_type=provider_type,
            api_format=config.api_format.value,
            is_active=config.is_active,
            priority=config.priority,
            max_requests_per_minute=config.max_requests_per_minute,
            timeout_seconds=config.timeout_seconds,
            settings=config.settings,
            created_by_user_id=created_by_user_id,
        )

        self.db.add(db_provider)
        await self.db.commit()
        await self.db.refresh(db_provider)

        info(f"Создан провайдер: {config.name} ({config.api_format.value})")

        # Перезагружаем менеджер провайдеров
        await dynamic_manager.reload_providers(force=True)

        return db_provider

    async def update_provider(
        self, provider_id: int, config: ProviderUpdateSchema
    ) -> SMSProvider:
        """
        Обновить существующий провайдер

        Args:
            provider_id: ID провайдера
            config: Схема обновления провайдера

        Returns:
            Обновленный провайдер

        Raises:
            ValueError: При ошибке валидации или если провайдер не найден
        """
        # Найти провайдера
        provider = await self.db.get(SMSProvider, provider_id)
        if not provider:
            raise ValueError(f"Провайдер с ID {provider_id} не найден")

        # Обновляем поля
        update_data = {}

        if config.display_name is not None:
            update_data["display_name"] = config.display_name
        if config.api_url is not None:
            update_data["api_url"] = config.api_url
        if config.username is not None:
            update_data["username"] = config.username
        if config.priority is not None:
            update_data["priority"] = config.priority
        if config.is_active is not None:
            update_data["is_active"] = config.is_active
        if config.max_requests_per_minute is not None:
            update_data["max_requests_per_minute"] = config.max_requests_per_minute
        if config.timeout_seconds is not None:
            update_data["timeout_seconds"] = config.timeout_seconds
        if config.settings is not None:
            update_data["settings"] = config.settings

        # Сохраняем новые учетные данные БЕЗ шифрования
        if config.api_key is not None:
            update_data["api_key"] = config.api_key  # Сохраняем в открытом виде
            debug(f"API ключ обновлен для провайдера {provider.name}")

        if config.password is not None:
            update_data["password"] = config.password  # Сохраняем в открытом виде
            debug(f"Пароль обновлен для провайдера {provider.name}")

        if update_data:
            update_data["updated_at"] = datetime.utcnow()

            await self.db.execute(
                update(SMSProvider)
                .where(SMSProvider.id == provider_id)
                .values(**update_data)
            )
            await self.db.commit()
            await self.db.refresh(provider)

            info(f"Обновлен провайдер: {provider.name}")

            # Перезагружаем менеджер провайдеров
            await dynamic_manager.reload_providers(force=True)

        return provider

    async def delete_provider(self, provider_id: int) -> bool:
        """
        Удалить провайдер

        Args:
            provider_id: ID провайдера

        Returns:
            True если провайдер был удален

        Raises:
            ValueError: Если провайдер не найден
        """
        provider = await self.db.get(SMSProvider, provider_id)
        if not provider:
            raise ValueError(f"Провайдер с ID {provider_id} не найден")

        provider_name = provider.name
        await self.db.delete(provider)
        await self.db.commit()

        info(f"Удален провайдер: {provider_name}")

        # Перезагружаем менеджер провайдеров
        await dynamic_manager.reload_providers(force=True)

        return True

    async def test_provider_api(self, provider_id: int) -> ProviderTestResponseSchema:
        """
        Протестировать API провайдера

        Args:
            provider_id: ID провайдера

        Returns:
            Результат тестирования
        """
        start_time = time.time()

        provider = await self.db.get(SMSProvider, provider_id)
        if not provider:
            return ProviderTestResponseSchema(
                success=False,
                status=ProviderStatus.FAILED,
                message="Провайдер не найден",
            )

        try:
            # Подготавливаем конфигурацию для тестирования
            config = {
                "name": provider.name,
                "api_url": provider.api_url,
                "api_format": provider.api_format,
                "api_key": provider.api_key,
                "username": provider.username,
                "password": provider.password,
                "timeout_seconds": provider.timeout_seconds,
                "settings": provider.settings or {},
            }

            # Создаем экземпляр провайдера для тестирования
            test_provider = self.factory.create_provider_from_config(config)

            # Тестируем получение баланса
            balance_result = await test_provider.get_balance()

            test_duration = time.time() - start_time

            if balance_result.get("success"):
                # Обновляем статус тестирования в БД
                await self.db.execute(
                    update(SMSProvider)
                    .where(SMSProvider.id == provider_id)
                    .values(
                        last_test_at=datetime.utcnow(),
                        last_test_result=ProviderStatus.SUCCESS.value,
                        test_error_message=None,
                    )
                )
                await self.db.commit()

                info(
                    f"Тест провайдера {provider.name} успешен, баланс: {balance_result.get('balance', 'N/A')}"
                )

                return ProviderTestResponseSchema(
                    success=True,
                    status=ProviderStatus.SUCCESS,
                    message=f"API работает, баланс: {balance_result.get('balance', 'N/A')} {balance_result.get('currency', '')}",
                    details=balance_result,
                    test_duration=test_duration,
                )
            else:
                error_message = balance_result.get("error", "Неизвестная ошибка")

                # Обновляем статус тестирования в БД
                await self.db.execute(
                    update(SMSProvider)
                    .where(SMSProvider.id == provider_id)
                    .values(
                        last_test_at=datetime.utcnow(),
                        last_test_result=ProviderStatus.FAILED.value,
                        test_error_message=error_message,
                    )
                )
                await self.db.commit()

                error(f"Тест провайдера {provider.name} неуспешен: {error_message}")

                return ProviderTestResponseSchema(
                    success=False,
                    status=ProviderStatus.FAILED,
                    message=f"Ошибка API: {error_message}",
                    details=balance_result,
                    test_duration=test_duration,
                )

        except Exception as e:
            test_duration = time.time() - start_time
            error_message = str(e)

            # Обновляем статус тестирования в БД
            await self.db.execute(
                update(SMSProvider)
                .where(SMSProvider.id == provider_id)
                .values(
                    last_test_at=datetime.utcnow(),
                    last_test_result=ProviderStatus.FAILED.value,
                    test_error_message=error_message,
                )
            )
            await self.db.commit()

            error(f"Ошибка тестирования провайдера {provider.name}: {e}")

            return ProviderTestResponseSchema(
                success=False,
                status=ProviderStatus.FAILED,
                message=f"Ошибка тестирования: {error_message}",
                test_duration=test_duration,
            )

    async def test_provider_connection(
        self, provider_config: Dict[str, Any]
    ) -> ProviderTestResponseSchema:
        """
        Протестировать соединение с провайдером без сохранения в БД

        Args:
            provider_config: Конфигурация провайдера для тестирования

        Returns:
            Результат тестирования
        """
        start_time = time.time()

        try:
            # Создаем экземпляр провайдера для тестирования
            test_provider = self.factory.create_provider_from_config(provider_config)

            # Тестируем получение баланса
            balance_result = await test_provider.get_balance()

            test_duration = time.time() - start_time

            if balance_result.get("success"):
                info(
                    f"Тест соединения с провайдером {provider_config.get('name', 'Unknown')} успешен"
                )

                return ProviderTestResponseSchema(
                    success=True,
                    status=ProviderStatus.SUCCESS,
                    message=f"Соединение успешно, баланс: {balance_result.get('balance', 'N/A')} {balance_result.get('currency', '')}",
                    details=balance_result,
                    test_duration=test_duration,
                )
            else:
                error_message = balance_result.get("error", "Неизвестная ошибка")
                error(
                    f"Тест соединения с провайдером {provider_config.get('name', 'Unknown')} неуспешен: {error_message}"
                )

                return ProviderTestResponseSchema(
                    success=False,
                    status=ProviderStatus.FAILED,
                    message=f"Ошибка соединения: {error_message}",
                    details=balance_result,
                    test_duration=test_duration,
                )

        except Exception as e:
            test_duration = time.time() - start_time
            error_message = str(e)
            error(
                f"Ошибка тестирования соединения с провайдером {provider_config.get('name', 'Unknown')}: {e}"
            )

            return ProviderTestResponseSchema(
                success=False,
                status=ProviderStatus.FAILED,
                message=f"Ошибка тестирования: {error_message}",
                test_duration=test_duration,
            )

    async def toggle_provider(self, provider_id: int, is_active: bool) -> SMSProvider:
        """
        Включить/отключить провайдер

        Args:
            provider_id: ID провайдера
            is_active: Новый статус активности

        Returns:
            Обновленный провайдер

        Raises:
            ValueError: Если провайдер не найден
        """
        provider = await self.db.get(SMSProvider, provider_id)
        if not provider:
            raise ValueError(f"Провайдер с ID {provider_id} не найден")

        await self.db.execute(
            update(SMSProvider)
            .where(SMSProvider.id == provider_id)
            .values(is_active=is_active, updated_at=datetime.utcnow())
        )
        await self.db.commit()
        await self.db.refresh(provider)

        status_text = "активирован" if is_active else "деактивирован"
        info(f"Провайдер {provider.name} {status_text}")

        # Перезагружаем менеджер провайдеров
        await dynamic_manager.reload_providers(force=True)

        return provider

    async def update_priority(self, provider_id: int, priority: int) -> SMSProvider:
        """
        Изменить приоритет провайдера

        Args:
            provider_id: ID провайдера
            priority: Новый приоритет

        Returns:
            Обновленный провайдер

        Raises:
            ValueError: Если провайдер не найден
        """
        provider = await self.db.get(SMSProvider, provider_id)
        if not provider:
            raise ValueError(f"Провайдер с ID {provider_id} не найден")

        await self.db.execute(
            update(SMSProvider)
            .where(SMSProvider.id == provider_id)
            .values(priority=priority, updated_at=datetime.utcnow())
        )
        await self.db.commit()
        await self.db.refresh(provider)

        info(f"Приоритет провайдера {provider.name} изменен на {priority}")

        # Перезагружаем менеджер провайдеров
        await dynamic_manager.reload_providers(force=True)

        return provider

    async def get_active_providers(self) -> List[SMSProvider]:
        """
        Получить список активных провайдеров

        Returns:
            Список активных провайдеров
        """
        result = await self.db.execute(
            select(SMSProvider)
            .where(SMSProvider.is_active == True)
            .order_by(SMSProvider.priority.asc())
        )
        return result.scalars().all()

    async def get_all_providers(self) -> List[SMSProvider]:
        """
        Получить список всех провайдеров

        Returns:
            Список всех провайдеров
        """
        result = await self.db.execute(
            select(SMSProvider).order_by(SMSProvider.priority.asc())
        )
        return result.scalars().all()

    async def get_provider_by_id(self, provider_id: int) -> Optional[SMSProvider]:
        """
        Получить провайдер по ID

        Args:
            provider_id: ID провайдера

        Returns:
            Провайдер или None
        """
        return await self.db.get(SMSProvider, provider_id)

    async def reload_providers_cache(self) -> bool:
        """
        Перезагрузить кеш провайдеров

        Returns:
            True если кеш был перезагружен
        """
        success = await dynamic_manager.reload_providers(force=True)
        if success:
            info("Кеш провайдеров перезагружен")
        return success

    def get_api_formats(self) -> Dict[str, str]:
        """
        Получить список поддерживаемых форматов API

        Returns:
            Словарь формат -> описание
        """
        return self.factory.get_supported_api_formats()
