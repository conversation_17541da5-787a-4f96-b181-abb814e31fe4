"""
Фабрика для создания провайдеров из конфигурации базы данных
"""
from __future__ import annotations

from typing import Any, Dict

from app.core.logger import debug, error
from app.providers.base import BaseProvider
from app.providers.generic_firefox import GenericFirefoxProvider
from app.providers.generic_smsactivate import GenericSMSActivateProvider
from app.schemas.sms_provider import APIFormat


class ProviderError(Exception):
    """Исключение при ошибках создания провайдера"""

    pass


class DynamicProviderFactory:
    """
    Фабрика для создания провайдеров на основе конфигурации из БД
    Поддерживает разные типы API (Firefox, SMSActivate)
    """

    def create_provider_from_config(
        self, provider_config: Dict[str, Any]
    ) -> BaseProvider:
        """
        Создать провайдер по конфигурации из БД

        Args:
            provider_config: Словарь с конфигурацией провайдера

        Returns:
            Экземпляр провайдера

        Raises:
            ProviderError: При ошибке создания провайдера
        """
        api_format = provider_config.get("api_format")
        provider_name = provider_config.get("name", "unknown")

        try:
            if api_format == APIFormat.SMSACTIVATE.value:
                return self._create_smsactivate_provider(provider_config)
            elif api_format == APIFormat.FIREFOX.value:
                return self._create_firefox_provider(provider_config)
            else:
                raise ProviderError(f"Неизвестный формат API: {api_format}")

        except Exception as e:
            error(f"Ошибка создания провайдера {provider_name}: {e}")
            raise ProviderError(
                f"Не удалось создать провайдер {provider_name}: {str(e)}"
            )

    def _create_smsactivate_provider(
        self, config: Dict[str, Any]
    ) -> GenericSMSActivateProvider:
        """
        Создать SMSActivate провайдер

        Args:
            config: Конфигурация провайдера

        Returns:
            Экземпляр GenericSMSActivateProvider
        """
        # Получаем API ключ напрямую (БЕЗ расшифровки)
        api_key = config.get("api_key")
        if not api_key:
            raise ProviderError("API ключ не указан для SMSActivate провайдера")

        # Подготавливаем конфигурацию
        provider_config = {
            "name": config.get("name"),
            "api_url": config.get("api_url"),
            "api_key": api_key,
            "timeout_seconds": config.get("timeout_seconds", 30),
            "max_requests_per_minute": config.get("max_requests_per_minute", 60),
            "settings": config.get("settings", {}),
        }

        debug(f"Создаем SMSActivate провайдер: {config.get('name')}")
        return GenericSMSActivateProvider(provider_config)

    def _create_firefox_provider(
        self, config: Dict[str, Any]
    ) -> GenericFirefoxProvider:
        """
        Создать Firefox провайдер

        Args:
            config: Конфигурация провайдера

        Returns:
            Экземпляр GenericFirefoxProvider
        """
        # Получаем учетные данные напрямую (БЕЗ расшифровки)
        password = config.get("password")
        if not password:
            raise ProviderError("Пароль не указан для Firefox провайдера")

        username = config.get("username")
        if not username:
            raise ProviderError("Логин не указан для Firefox провайдера")

        # Подготавливаем конфигурацию
        provider_config = {
            "name": config.get("name"),
            "api_url": config.get("api_url"),
            "username": username,
            "password": password,
            "timeout_seconds": config.get("timeout_seconds", 30),
            "max_requests_per_minute": config.get("max_requests_per_minute", 60),
            "settings": config.get("settings", {}),
        }

        debug(f"Создаем Firefox провайдер: {config.get('name')}")
        return GenericFirefoxProvider(provider_config)

    def test_provider_creation(self, provider_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Протестировать создание провайдера без фактического создания

        Args:
            provider_config: Конфигурация провайдера

        Returns:
            Результат тестирования
        """
        try:
            # Проверяем обязательные поля
            if not provider_config.get("name"):
                return {"success": False, "error": "Имя провайдера не указано"}

            if not provider_config.get("api_url"):
                return {"success": False, "error": "URL API не указан"}

            api_format = provider_config.get("api_format")
            if not api_format:
                return {"success": False, "error": "Формат API не указан"}

            # Проверяем специфичные для типа поля
            if api_format == APIFormat.SMSACTIVATE.value:
                if not provider_config.get("api_key"):
                    return {
                        "success": False,
                        "error": "API ключ обязателен для SMSActivate",
                    }

            elif api_format == APIFormat.FIREFOX.value:
                if not provider_config.get("username"):
                    return {"success": False, "error": "Логин обязателен для Firefox"}
                if not provider_config.get("password"):
                    return {"success": False, "error": "Пароль обязателен для Firefox"}

            else:
                return {
                    "success": False,
                    "error": f"Неподдерживаемый формат API: {api_format}",
                }

            return {"success": True, "message": "Конфигурация валидна"}

        except Exception as e:
            error(f"Ошибка тестирования конфигурации: {e}")
            return {"success": False, "error": str(e)}

    def get_supported_api_formats(self) -> Dict[str, str]:
        """
        Получить список поддерживаемых форматов API

        Returns:
            Словарь формат -> описание
        """
        return {
            APIFormat.SMSACTIVATE.value: "SMSActivate совместимый API",
            APIFormat.FIREFOX.value: "Firefox совместимый API",
        }
