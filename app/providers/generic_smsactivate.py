"""
Универсальный провайдер для SMSActivate-совместимых API
"""
from __future__ import annotations

import asyncio
from typing import Any, Dict

import aiohttp

from app.core.logger import debug, error, info
from app.providers.base import BaseProvider


class GenericSMSActivateProvider(BaseProvider):
    """
    Универсальный провайдер для SMSActivate-совместимых API
    Поддерживает стандартный формат запросов SMSActivate
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализация SMSActivate провайдера

        Args:
            config: Конфигурация провайдера с api_key
        """
        super().__init__(config)
        self.api_key = config.get("api_key")

        if not self.api_key:
            raise ValueError("API ключ обязателен для SMSActivate провайдера")

    async def _make_request(self, params: Dict[str, str]) -> Dict[str, Any]:
        """
        Выполнить HTTP запрос к API

        Args:
            params: Параметры запроса

        Returns:
            Dict с результатом запроса:
            {
                "success": bool,
                "response": str,
                "status_code": int,
                "error": str (если success=False)
            }

        Raises:
            Exception: При критической ошибке запроса
        """
        # Добавляем API ключ ко всем запросам
        params["api_key"] = self.api_key

        # Формируем правильный URL для SMSActivate API
        api_url = self.api_url

        # Если URL заканчивается на /, добавляем стандартный endpoint SMSActivate
        if api_url.endswith("/") and not api_url.endswith("/stubs/handler_api.php"):
            api_url = api_url.rstrip("/") + "/stubs/handler_api.php"

        # Если URL не содержит стандартного endpoint, добавляем его
        elif not api_url.endswith("handler_api.php") and not api_url.endswith(".php"):
            if not api_url.endswith("/"):
                api_url += "/"
            api_url += "stubs/handler_api.php"

        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                debug(
                    f"Отправляем запрос к {self.name}: {api_url} с параметрами: {params}"
                )

                async with session.get(api_url, params=params) as response:
                    response_text = await response.text()
                    status_code = response.status

                    debug(
                        f"SMSActivate API ответ от {self.name}: статус {status_code}, тело: {response_text}"
                    )

                    if status_code == 200:
                        return {
                            "success": True,
                            "response": response_text,
                            "status_code": status_code,
                        }
                    else:
                        return {
                            "success": False,
                            "response": response_text,
                            "status_code": status_code,
                            "error": f"HTTP статус {status_code}: {response_text}",
                        }

        except asyncio.TimeoutError:
            error_msg = f"Таймаут запроса к {self.name} (>{self.timeout}с)"
            error(error_msg)
            return {"success": False, "error": error_msg, "status_code": 0}
        except Exception as e:
            error_msg = f"Ошибка запроса к {self.name}: {str(e)}"
            error(error_msg)
            return {"success": False, "error": error_msg, "status_code": 0}

    async def get_number(
        self, service_code: str, country_code: int = 0, **kwargs
    ) -> Dict[str, Any]:
        """
        Получить номер в формате SMSActivate API

        Пример запроса: ?api_key=xxx&action=getNumber&service=tg&country=0
        """
        try:
            params = {
                "action": "getNumber",
                "service": service_code,
                "country": str(country_code),
            }

            # Добавляем дополнительные параметры если указаны
            if "maxPrice" in kwargs:
                params["maxPrice"] = str(kwargs["maxPrice"])
            if "operator" in kwargs:
                params["operator"] = kwargs["operator"]

            request_result = await self._make_request(params)

            if not request_result["success"]:
                # Возвращаем детали ошибки запроса
                return {
                    "success": False,
                    "error": request_result["error"],
                    "status_code": request_result.get("status_code"),
                    "response": request_result.get("response"),
                }

            response = request_result["response"]

            # Парсим ответ SMSActivate формата
            if response.startswith("ACCESS_NUMBER:"):
                # Формат: ACCESS_NUMBER:ID:PHONE
                parts = response.split(":")
                if len(parts) >= 3:
                    activation_id = parts[1]
                    phone_number = parts[2]

                    info(f"Номер получен от {self.name}: {phone_number}")
                    return {
                        "success": True,
                        "activation_id": activation_id,
                        "phone_number": phone_number,
                        "cost": 0.0,  # SMSActivate не возвращает стоимость в этом запросе
                        "provider": self.name,
                        "response": response,
                        "status_code": request_result.get("status_code", 200),
                    }

            # Обработка ошибок SMSActivate
            error_messages = {
                "NO_NUMBERS": "Нет доступных номеров",
                "NO_BALANCE": "Недостаточно средств",
                "BAD_ACTION": "Неверное действие",
                "BAD_SERVICE": "Неподдерживаемый сервис",
                "BAD_KEY": "Неверный API ключ",
            }

            error_message = error_messages.get(
                response, f"Неизвестный ответ от API: {response}"
            )

            return {
                "success": False,
                "error": error_message,
                "response": response,
                "status_code": request_result.get("status_code", 200),
                "error_code": response,
            }

        except Exception as e:
            error_msg = f"Ошибка получения номера от {self.name}: {str(e)}"
            error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "exception_type": type(e).__name__,
            }

    async def get_status(self, activation_id: str) -> Dict[str, Any]:
        """
        Получить статус активации в формате SMSActivate API

        Пример запроса: ?api_key=xxx&action=getStatus&id=123
        """
        try:
            params = {"action": "getStatus", "id": activation_id}

            request_result = await self._make_request(params)

            if not request_result["success"]:
                # Возвращаем детали ошибки запроса
                return {
                    "success": False,
                    "error": request_result["error"],
                    "status_code": request_result.get("status_code"),
                    "response": request_result.get("response"),
                }

            response = request_result["response"]

            # Парсим ответ SMSActivate
            if response.startswith("STATUS_OK:"):
                # Формат: STATUS_OK:CODE
                sms_code = response.split(":", 1)[1]
                info(f"SMS код получен от {self.name}: {sms_code}")
                return {
                    "success": True,
                    "status": "completed",
                    "sms_code": sms_code,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                }
            elif response == "STATUS_WAIT_CODE":
                return {
                    "success": True,
                    "status": "waiting_for_sms",
                    "sms_code": None,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                }
            elif response == "STATUS_WAIT_RETRY":
                return {
                    "success": True,
                    "status": "waiting_for_retry",
                    "sms_code": None,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                }
            elif response == "STATUS_CANCEL":
                return {
                    "success": True,
                    "status": "cancelled",
                    "sms_code": None,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                }
            else:
                return {
                    "success": False,
                    "error": f"Неизвестный статус от API: {response}",
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                    "error_code": response,
                }

        except Exception as e:
            error_msg = f"Ошибка получения статуса от {self.name}: {str(e)}"
            error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "exception_type": type(e).__name__,
            }

    async def cancel_activation(self, activation_id: str) -> Dict[str, Any]:
        """
        Отменить активацию в формате SMSActivate API

        Пример запроса: ?api_key=xxx&action=setStatus&status=8&id=123
        """
        try:
            params = {
                "action": "setStatus",
                "status": "8",  # 8 = отмена в SMSActivate
                "id": activation_id,
            }

            request_result = await self._make_request(params)

            if not request_result["success"]:
                # Возвращаем детали ошибки запроса
                return {
                    "success": False,
                    "error": request_result["error"],
                    "status_code": request_result.get("status_code"),
                    "response": request_result.get("response"),
                }

            response = request_result["response"]

            if response == "ACCESS_CANCEL":
                info(f"Активация отменена в {self.name}: {activation_id}")
                return {
                    "success": True,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                }
            else:
                return {
                    "success": False,
                    "error": f"Не удалось отменить активацию: {response}",
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                    "error_code": response,
                }

        except Exception as e:
            error_msg = f"Ошибка отмены активации в {self.name}: {str(e)}"
            error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "exception_type": type(e).__name__,
            }

    async def get_balance(self) -> Dict[str, Any]:
        """
        Получить баланс в формате SMSActivate API

        Пример запроса: ?api_key=xxx&action=getBalance
        """
        try:
            params = {"action": "getBalance"}
            request_result = await self._make_request(params)

            if not request_result["success"]:
                # Возвращаем детали ошибки запроса
                return {
                    "success": False,
                    "error": request_result["error"],
                    "status_code": request_result.get("status_code"),
                    "response": request_result.get("response"),
                }

            response = request_result["response"]

            if response.startswith("ACCESS_BALANCE:"):
                # Формат: ACCESS_BALANCE:BALANCE
                balance_str = response.split(":", 1)[1]
                try:
                    balance = float(balance_str)
                    debug(f"Баланс {self.name}: {balance}")
                    return {
                        "success": True,
                        "balance": balance,
                        "currency": "RUB",
                        "response": response,
                        "status_code": request_result.get("status_code", 200),
                    }
                except ValueError as e:
                    return {
                        "success": False,
                        "error": f"Не удалось преобразовать баланс '{balance_str}' в число: {str(e)}",
                        "response": response,
                        "status_code": request_result.get("status_code", 200),
                    }
            else:
                # Определяем тип ошибки по ответу SMSActivate
                error_messages = {
                    "BAD_KEY": "Неверный API ключ",
                    "ERROR_SQL": "Ошибка в базе данных провайдера",
                    "BAD_ACTION": "Неподдерживаемое действие",
                    "NO_ACTIVATION": "Активация не найдена",
                    "BAD_SERVICE": "Неподдерживаемый сервис",
                }

                error_message = error_messages.get(
                    response, f"Неизвестный ответ от API: {response}"
                )

                return {
                    "success": False,
                    "error": error_message,
                    "response": response,
                    "status_code": request_result.get("status_code", 200),
                    "error_code": response,
                }

        except Exception as e:
            error_msg = f"Ошибка получения баланса от {self.name}: {str(e)}"
            error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "exception_type": type(e).__name__,
            }
