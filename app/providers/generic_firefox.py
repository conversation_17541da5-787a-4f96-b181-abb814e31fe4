"""
Универсальный провайдер для Firefox-совместимых API
"""
from __future__ import annotations

import asyncio
from typing import Any, Dict, Optional

import aiohttp

from app.core.logger import debug, error, info
from app.providers.base import BaseProvider


class GenericFirefoxProvider(BaseProvider):
    """
    Универсальный провайдер для Firefox-совместимых API
    Поддерживает стандартный формат запросов Firefox с токен-авторизацией
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализация Firefox провайдера

        Args:
            config: Конфигурация провайдера с username и password
        """
        super().__init__(config)
        self.username = config.get("username")
        self.password = config.get("password")
        self._token: Optional[str] = None

        if not self.username or not self.password:
            raise ValueError("Логин и пароль обязательны для Firefox провайдера")

    def _get_mapping_service(self):
        """
        Получить экземпляр сервиса маппинга (ленивый импорт для избежания циклических зависимостей)

        Returns:
            Экземпляр MappingService
        """
        from app.services.mapping_service import mapping_service

        return mapping_service

    async def _make_request(self, params: Dict[str, str]) -> str:
        """
        Выполнить HTTP запрос к Firefox API

        Args:
            params: Параметры запроса

        Returns:
            Ответ сервера как строка

        Raises:
            Exception: При ошибке запроса
        """
        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            ) as session:
                debug(f"Firefox API запрос: {self.api_url} с параметрами: {params}")
                async with session.get(self.api_url, params=params) as response:
                    response_text = await response.text()
                    debug(
                        f"Firefox API ответ: статус {response.status}, тело: '{response_text}'"
                    )
                    return response_text

        except asyncio.TimeoutError:
            error(f"Таймаут запроса к {self.name}")
            raise Exception("Таймаут запроса к API")
        except Exception as e:
            error(f"Ошибка запроса к {self.name}: {e}")
            raise

    async def _authenticate(self) -> str:
        """
        Выполнить авторизацию и получить токен

        Returns:
            Токен авторизации

        Raises:
            Exception: При ошибке авторизации
        """
        params = {"act": "login", "ApiName": self.username, "PassWord": self.password}

        response = await self._make_request(params)

        # Firefox API возвращает формат: "1|token" при успехе или "0|error_code" при ошибке
        if "|" in response:
            status_code, data = response.split("|", 1)

            if status_code == "1":
                # Успешная авторизация - data содержит токен
                token = data.strip()
                self._token = token
                info(f"Авторизация в {self.name} успешна")
                return token
            else:
                # Ошибка авторизации - data содержит код ошибки
                error_codes = {
                    "-1": "Неверные учетные данные",
                    "-2": "Недействительный токен",
                    "-3": "Слишком частые запросы - подождите 60 секунд",
                    "-9": "Неверные учетные данные или заблокированный аккаунт",
                    "0": "Общая ошибка",
                }
                error_message = error_codes.get(data, f"Неизвестная ошибка: {data}")
                raise Exception(f"Ошибка авторизации: {error_message}")
        else:
            # Неожиданный формат ответа
            raise Exception(f"Неожиданный формат ответа авторизации: {response}")

    async def _get_token(self) -> str:
        """
        Получить актуальный токен (с повторной авторизацией при необходимости)

        Returns:
            Токен авторизации
        """
        if not self._token:
            return await self._authenticate()
        return self._token

    def _convert_service_code(self, service_code: str) -> str:
        """
        Конвертировать код сервиса SMSActivate в Firefox формат

        Args:
            service_code: Код сервиса SMSActivate (tg, wa, vk)

        Returns:
            Код сервиса Firefox (1000, 1001, 1010)

        Raises:
            ValueError: Если маппинг для сервиса не найден
        """
        # Используем сервис маппинга для получения Firefox IID
        mapping_service = self._get_mapping_service()
        firefox_iid = mapping_service.get_firefox_service_iid(service_code)

        if firefox_iid:
            debug(f"Конвертация сервиса: {service_code} -> {firefox_iid}")
            return firefox_iid
        else:
            # Логгируем техническую ошибку в лог файл
            error_msg = f"Маппинг для сервиса '{service_code}' не найден в mappings.txt. Пожалуйста, добавьте маппинг в формате: {service_code}|firefox_service_id|service_name"
            error(error_msg)
            # Возвращаем пользовательскую ошибку
            raise ValueError("Bad service")

    def _convert_country_code(self, country_code: int) -> str:
        """
        Конвертировать код страны SMSActivate в Firefox формат

        Args:
            country_code: Код страны SMSActivate (0, 1, 2)

        Returns:
            Код страны Firefox (rus, ukr, kaz)

        Raises:
            ValueError: Если маппинг для страны не найден
        """
        # Используем сервис маппинга для получения Firefox кода страны
        mapping_service = self._get_mapping_service()
        firefox_country = mapping_service.get_firefox_country_code(country_code)

        if firefox_country:
            debug(f"Конвертация страны: {country_code} -> {firefox_country}")
            return firefox_country
        else:
            # Логгируем техническую ошибку в лог файл
            error_msg = f"Маппинг для страны '{country_code}' не найден в mappings.txt. Пожалуйста, добавьте маппинг в формате: {country_code}|firefox_country_code|calling_code|country_name"
            error(error_msg)
            # Возвращаем пользовательскую ошибку
            raise ValueError("Bad country")

    def _get_calling_code_by_firefox_country_id(self, firefox_country_id: str) -> str:
        """
        Получить телефонный код страны по коду страны Firefox

        Args:
            firefox_country_id: Буквенный код страны Firefox (rus, usa, ukr и т.д.)

        Returns:
            Телефонный код страны (7, 380, 86 и т.д.)

        Raises:
            ValueError: Если маппинг для Firefox country code не найден
        """
        mapping_service = self._get_mapping_service()
        all_countries = mapping_service.get_all_countries()

        # Firefox использует только буквенные коды стран (rus, usa, ukr)
        # Цифровые ID принадлежат только SMSActivate
        for sms_country_code, (
            firefox_code,
            calling_code,
            country_name,
        ) in all_countries.items():
            if firefox_code == firefox_country_id:
                debug(
                    f"Найден телефонный код для Firefox '{firefox_country_id}': {calling_code}"
                )
                return calling_code

        # Если не найден - логгируем техническую ошибку и возвращаем пользовательскую
        available_codes = [
            firefox_code for _, (firefox_code, _, _) in all_countries.items()
        ]
        error_msg = f"Телефонный код для Firefox country '{firefox_country_id}' не найден в mappings.txt. Доступные Firefox коды: {available_codes}"
        error(error_msg)
        # Возвращаем пользовательскую ошибку
        raise ValueError("Bad country")

    async def get_number(
        self, service_code: str, country_code: int = 0, **kwargs
    ) -> Dict[str, Any]:
        """
        Получить номер в формате Firefox API

        Пример запроса: ?act=getPhone&token=xxx&iid=1000&country=rus
        Ответ успех: 1|pkey|getTime|countryId|area|city|protName|mobile|dockCode
        Ответ ошибка: 0|error_code
        """
        try:
            # Конвертируем коды в Firefox формат с использованием сервиса маппинга
            firefox_service = self._convert_service_code(service_code)
            firefox_country = self._convert_country_code(country_code)

            token = await self._get_token()

            params = {
                "act": "getPhone",
                "token": token,
                "iid": firefox_service,
                "country": firefox_country,
                "dock": "0",  # Добавляем dock параметр
            }

            response = await self._make_request(params)

            # Парсим ответ Firefox в формате: 1|pkey|getTime|countryId|area|city|protName|mobile|dockCode
            if "|" in response:
                parts = response.split("|")

                if parts[0] == "1" and len(parts) >= 8:
                    # Успешный ответ
                    pkey = parts[1]  # Внутренний ID Firefox (activation_id)
                    mobile = parts[7]  # Локальный номер без кода страны
                    country_id = parts[3]  # ID страны Firefox

                    # Получаем телефонный код для данной страны Firefox
                    calling_code = self._get_calling_code_by_firefox_country_id(
                        country_id
                    )
                    phone_number = f"{calling_code}{mobile}"

                    info(f"Номер получен от {self.name}: +{phone_number}")
                    return {
                        "success": True,
                        "activation_id": pkey,
                        "phone_number": phone_number,
                        "cost": 0.0,
                        "provider": self.name,
                        "response": response,
                    }

                elif parts[0] == "0":
                    # Ошибка - получаем код ошибки
                    error_code = parts[1] if len(parts) > 1 else "unknown"
                    error_messages = {
                        "-1": "NO_NUMBERS",  # Нет доступных номеров
                        "-2": "BAD_TOKEN",  # Недействительный токен
                        "-3": "BAD_SERVICE",  # Сервис не существует
                        "-4": "BAD_COUNTRY",  # Ошибка страны
                        "-5": "BAD_SERVICE",  # Сервис не проверен
                        "-6": "BAD_SERVICE",  # Сервис отключен
                        "-7": "BAD_TOKEN",  # Токен отключен
                        "-8": "NO_BALANCE",  # Недостаточно средств
                        "-9": "NO_NUMBERS",  # Слишком много номеров, пополните баланс
                        "-10": "BAD_SERVICE",  # Сервис не позволяет указывать номер
                    }

                    # Логгируем техническую информацию
                    error(f"Firefox API ошибка: код {error_code}")

                    # Получаем пользовательское сообщение
                    user_error = error_messages.get(error_code, "UNKNOWN_ERROR")

                    if error_code == "-2" or error_code == "-7":
                        # Проблемы с токеном - пробуем повторную авторизацию
                        self._token = None
                        return await self.get_number(
                            service_code, country_code, **kwargs
                        )

                    return {
                        "success": False,
                        "error": user_error,
                        "error_code": error_code,
                    }

            # Неожиданный формат ответа
            return {"success": False, "error": f"Неожиданный формат ответа: {response}"}

        except Exception as e:
            error(f"Ошибка получения номера от {self.name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_status(self, activation_id: str) -> Dict[str, Any]:
        """
        Получить статус активации в формате Firefox API

        Пример запроса: ?act=getPhoneCode&token=xxx&pkey=123456
        Ответ успех: 1|code|sms
        Ответ ошибка: 0|error_code
        """
        try:
            token = await self._get_token()

            params = {"act": "getPhoneCode", "token": token, "pkey": activation_id}

            response = await self._make_request(params)

            # Парсим ответ Firefox в формате: 1|code|sms или 0|error_code
            if "|" in response:
                parts = response.split("|")

                if parts[0] == "1" and len(parts) >= 2:
                    # Успешный ответ - получен SMS код
                    sms_code = parts[1]
                    info(f"SMS код получен от {self.name}: {sms_code}")
                    return {
                        "success": True,
                        "status": "completed",
                        "sms_code": sms_code,
                    }

                elif parts[0] == "0":
                    # Ошибка или ожидание
                    error_code = parts[1] if len(parts) > 1 else "unknown"

                    if error_code == "-3":
                        # Код еще не пришел
                        return {
                            "success": True,
                            "status": "waiting_for_sms",
                            "sms_code": None,
                        }
                    elif error_code in ["-4", "-5"]:
                        # Номер отменен или проблемы
                        return {
                            "success": True,
                            "status": "cancelled",
                            "sms_code": None,
                        }
                    elif error_code == "-2":
                        # Проблемы с токеном
                        self._token = None
                        return await self.get_status(activation_id)
                    else:
                        return {
                            "success": False,
                            "error": f"Ошибка получения SMS: код {error_code}",
                        }

            return {"success": False, "error": f"Неожиданный формат ответа: {response}"}

        except Exception as e:
            error(f"Ошибка получения статуса от {self.name}: {e}")
            return {"success": False, "error": str(e)}

    async def cancel_activation(self, activation_id: str) -> Dict[str, Any]:
        """
        Отменить активацию в формате Firefox API

        Пример запроса: ?act=setRel&token=xxx&pkey=123456
        """
        try:
            token = await self._get_token()

            params = {
                "act": "setRel",
                "token": token,
                "pkey": activation_id,
            }

            response = await self._make_request(params)

            # Парсим ответ Firefox
            if "|" in response:
                parts = response.split("|")

                if parts[0] == "1":
                    info(f"Активация отменена в {self.name}: {activation_id}")
                    return {"success": True}
                elif parts[0] == "0":
                    error_code = parts[1] if len(parts) > 1 else "unknown"
                    if error_code == "-2":
                        # Проблемы с токеном
                        self._token = None
                        return await self.cancel_activation(activation_id)
                    else:
                        return {
                            "success": False,
                            "error": f"Не удалось отменить: код {error_code}",
                        }

            return {"success": False, "error": f"Неожиданный формат ответа: {response}"}

        except Exception as e:
            error(f"Ошибка отмены активации в {self.name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_balance(self) -> Dict[str, Any]:
        """
        Получить баланс в формате Firefox API

        Пример запроса: ?act=myInfo&token=xxx
        Ответ: 1|balance|level|integral или 0|error_code
        """
        try:
            token = await self._get_token()

            params = {"act": "myInfo", "token": token}

            response = await self._make_request(params)

            # Парсим ответ Firefox в формате: 1|balance|level|integral или 0|error_code
            if "|" in response:
                parts = response.split("|")

                if parts[0] == "1" and len(parts) >= 2:
                    # Успешный ответ - balance находится во втором элементе
                    try:
                        balance = float(parts[1])
                        debug(f"Баланс {self.name}: {balance}")
                        return {"success": True, "balance": balance, "currency": "RUB"}
                    except ValueError:
                        return {
                            "success": False,
                            "error": f"Не удалось преобразовать баланс: {parts[1]}",
                        }

                elif parts[0] == "0":
                    # Ошибка
                    error_code = parts[1] if len(parts) > 1 else "unknown"
                    if error_code == "-2":
                        # Проблемы с токеном - пробуем повторную авторизацию
                        self._token = None
                        return await self.get_balance()
                    else:
                        error_messages = {
                            "-1": "Токен не существует",
                            "-3": "Слишком частые запросы - подождите 60 секунд",
                        }
                        error_message = error_messages.get(
                            error_code, f"Ошибка получения баланса: код {error_code}"
                        )
                        return {"success": False, "error": error_message}

            # Неожиданный формат ответа
            return {"success": False, "error": f"Неожиданный формат ответа: {response}"}

        except Exception as e:
            error(f"Ошибка получения баланса от {self.name}: {e}")
            return {"success": False, "error": str(e)}
