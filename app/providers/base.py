"""
Базовый интерфейс для всех SMS провайдеров
"""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict


class BaseProvider(ABC):
    """
    Базовый абстрактный класс для всех SMS провайдеров
    Определяет единый интерфейс для работы с разными типами API
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализация провайдера

        Args:
            config: Словарь с конфигурацией провайдера
        """
        self.name = config.get("name")
        self.api_url = config.get("api_url")
        self.timeout = config.get("timeout_seconds", 30)
        self.max_requests_per_minute = config.get("max_requests_per_minute", 60)
        self.settings = config.get("settings", {})

    @abstractmethod
    async def get_number(
        self, service_code: str, country_code: int = 0, **kwargs
    ) -> Dict[str, Any]:
        """
        Получить номер телефона для активации

        Args:
            service_code: Код сервиса (tg, wa, vk и т.д.)
            country_code: Код страны (0=Россия, 1=Украина и т.д.)
            **kwargs: Дополнительные параметры (maxPrice, operator и т.д.)

        Returns:
            Dict с результатом:
            {
                "success": bool,
                "activation_id": str,
                "phone_number": str,
                "cost": float,
                "error": str (если success=False)
            }
        """
        pass

    @abstractmethod
    async def get_status(self, activation_id: str) -> Dict[str, Any]:
        """
        Получить статус активации и SMS код

        Args:
            activation_id: ID активации

        Returns:
            Dict с результатом:
            {
                "success": bool,
                "status": str,
                "sms_code": str (если получен),
                "error": str (если success=False)
            }
        """
        pass

    @abstractmethod
    async def cancel_activation(self, activation_id: str) -> Dict[str, Any]:
        """
        Отменить активацию

        Args:
            activation_id: ID активации

        Returns:
            Dict с результатом:
            {
                "success": bool,
                "error": str (если success=False)
            }
        """
        pass

    @abstractmethod
    async def get_balance(self) -> Dict[str, Any]:
        """
        Получить баланс аккаунта

        Returns:
            Dict с результатом:
            {
                "success": bool,
                "balance": float,
                "currency": str,
                "error": str (если success=False)
            }
        """
        pass

    def __repr__(self):
        return f"<{self.__class__.__name__}(name='{self.name}')>"
