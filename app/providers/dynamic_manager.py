"""
Динамический менеджер провайдеров с загрузкой из базы данных
"""
from __future__ import annotations

import time
from typing import Any, Dict, List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import async_session_maker
from app.core.logger import debug, error, info, warning
from app.models.sms_provider import SMSProvider
from app.providers.base import BaseProvider
from app.providers.dynamic_factory import DynamicProviderFactory, ProviderError


class DynamicProviderManager:
    """
    Менеджер для работы с динамически загружаемыми провайдерами
    Загружает провайдеров из БД и управляет ими
    """

    def __init__(self):
        """Инициализация менеджера"""
        self.factory = DynamicProviderFactory()
        self.providers: List[BaseProvider] = []
        self.provider_configs: List[Dict[str, Any]] = []
        self.last_reload_time = 0
        self.cache_ttl = 300  # 5 минут кеш

    async def reload_providers(self, force: bool = False) -> bool:
        """
        Перезагрузить провайдеров из БД

        Args:
            force: Принудительная перезагрузка без учета кеша

        Returns:
            True если провайдеры были перезагружены
        """
        current_time = time.time()

        # Проверяем нужна ли перезагрузка
        if not force and (current_time - self.last_reload_time) < self.cache_ttl:
            return False

        try:
            debug("Перезагружаем провайдеров из БД")

            # Загружаем активных провайдеров из БД
            async with async_session_maker() as session:
                result = await session.execute(
                    select(SMSProvider)
                    .where(SMSProvider.is_active == True)
                    .order_by(SMSProvider.priority.asc())
                )
                db_providers = result.scalars().all()

            # Конвертируем в конфигурации
            new_configs = []
            for db_provider in db_providers:
                config = {
                    "id": db_provider.id,
                    "name": db_provider.name,
                    "display_name": db_provider.display_name,
                    "api_url": db_provider.api_url,
                    "api_format": db_provider.api_format,
                    "provider_type": db_provider.provider_type,
                    "api_key": db_provider.api_key,
                    "username": db_provider.username,
                    "password": db_provider.password,
                    "priority": db_provider.priority,
                    "timeout_seconds": db_provider.timeout_seconds,
                    "max_requests_per_minute": db_provider.max_requests_per_minute,
                    "settings": db_provider.settings or {},
                }
                new_configs.append(config)

            # Создаем новых провайдеров
            new_providers = []
            for config in new_configs:
                try:
                    provider = self.factory.create_provider_from_config(config)
                    new_providers.append(provider)
                    debug(
                        f"Провайдер создан: {config['name']} ({config['api_format']})"
                    )
                except ProviderError as e:
                    error(f"Не удалось создать провайдер {config['name']}: {e}")
                    continue

            # Обновляем списки
            self.providers = new_providers
            self.provider_configs = new_configs
            self.last_reload_time = current_time

            info(f"Загружено {len(self.providers)} активных провайдеров")
            return True

        except Exception as e:
            error(f"Ошибка перезагрузки провайдеров: {e}")
            return False

    async def get_active_providers(self) -> List[BaseProvider]:
        """
        Получить список активных провайдеров

        Returns:
            Список провайдеров отсортированных по приоритету
        """
        # Перезагружаем если нужно
        await self.reload_providers()
        return self.providers.copy()

    async def get_number(
        self,
        service_code: str,
        country_code: int = 0,
        preferred_provider: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Получить номер от одного из провайдеров

        Args:
            service_code: Код сервиса (tg, wa, vk и т.д.)
            country_code: Код страны (0=Россия, 1=Украина и т.д.)
            preferred_provider: Предпочитаемое имя провайдера
            **kwargs: Дополнительные параметры (maxPrice, operator)

        Returns:
            Результат получения номера
        """
        providers = await self.get_active_providers()

        if not providers:
            return {"success": False, "error": "Нет доступных провайдеров"}

        # Определяем порядок провайдеров
        providers_to_try = []

        # Сначала предпочитаемый провайдер если указан
        if preferred_provider:
            preferred = next(
                (p for p in providers if p.name == preferred_provider), None
            )
            if preferred:
                providers_to_try.append(preferred)
                # Добавляем остальных как fallback
                providers_to_try.extend(
                    [p for p in providers if p.name != preferred_provider]
                )
            else:
                warning(f"Предпочитаемый провайдер {preferred_provider} не найден")
                providers_to_try = providers
        else:
            providers_to_try = providers

        last_error = None

        # Перебираем провайдеров
        for provider in providers_to_try:
            try:
                debug(
                    f"Попытка получения номера от {provider.name}, сервис: {service_code}"
                )

                result = await provider.get_number(service_code, country_code, **kwargs)

                if result.get("success"):
                    result["provider"] = provider.name
                    info(
                        f"Номер получен от {provider.name}: {result.get('phone_number', '')}"
                    )
                    return result
                else:
                    debug(
                        f"Провайдер {provider.name} не смог предоставить номер: {result.get('error')}"
                    )
                    last_error = result.get("error", "Unknown error")
                    continue

            except Exception as e:
                error(f"Ошибка провайдера {provider.name}: {e}")
                last_error = str(e)
                continue

        # Если никто не смог предоставить номер
        return {"success": False, "error": last_error or "NO_NUMBERS", "provider": None}

    async def get_status(
        self, activation_id: str, provider_name: str
    ) -> Dict[str, Any]:
        """
        Получить статус активации от конкретного провайдера

        Args:
            activation_id: ID активации
            provider_name: Имя провайдера

        Returns:
            Результат получения статуса
        """
        providers = await self.get_active_providers()

        # Находим нужный провайдер
        provider = next((p for p in providers if p.name == provider_name), None)
        if not provider:
            return {
                "success": False,
                "error": f"Провайдер {provider_name} не найден или неактивен",
            }

        try:
            return await provider.get_status(activation_id)
        except Exception as e:
            error(f"Ошибка получения статуса от {provider_name}: {e}")
            return {"success": False, "error": str(e)}

    async def cancel_activation(
        self, activation_id: str, provider_name: str
    ) -> Dict[str, Any]:
        """
        Отменить активацию у конкретного провайдера

        Args:
            activation_id: ID активации
            provider_name: Имя провайдера

        Returns:
            Результат отмены
        """
        providers = await self.get_active_providers()

        # Находим нужный провайдер
        provider = next((p for p in providers if p.name == provider_name), None)
        if not provider:
            return {
                "success": False,
                "error": f"Провайдер {provider_name} не найден или неактивен",
            }

        try:
            return await provider.cancel_activation(activation_id)
        except Exception as e:
            error(f"Ошибка отмены активации в {provider_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_balance(self, provider_name: str) -> Dict[str, Any]:
        """
        Получить баланс конкретного провайдера

        Args:
            provider_name: Имя провайдера

        Returns:
            Результат получения баланса
        """
        providers = await self.get_active_providers()

        # Находим нужный провайдер
        provider = next((p for p in providers if p.name == provider_name), None)
        if not provider:
            return {
                "success": False,
                "error": f"Провайдер {provider_name} не найден или неактивен",
            }

        try:
            return await provider.get_balance()
        except Exception as e:
            error(f"Ошибка получения баланса от {provider_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_provider(self, provider_name: str) -> Optional[BaseProvider]:
        """
        Получить конкретный провайдер по имени

        Args:
            provider_name: Имя провайдера

        Returns:
            Экземпляр провайдера или None
        """
        providers = await self.get_active_providers()
        return next((p for p in providers if p.name == provider_name), None)

    async def request_retry(
        self, activation_id: str, provider_name: str
    ) -> Dict[str, Any]:
        """
        Запросить повторный SMS код от конкретного провайдера

        Args:
            activation_id: ID активации
            provider_name: Имя провайдера

        Returns:
            Результат запроса повторного кода
        """
        providers = await self.get_active_providers()

        # Находим нужный провайдер
        provider = next((p for p in providers if p.name == provider_name), None)
        if not provider:
            return {
                "success": False,
                "error": f"Провайдер {provider_name} не найден или неактивен",
            }

        try:
            # Проверяем есть ли у провайдера метод request_retry
            if hasattr(provider, "request_retry"):
                return await provider.request_retry(activation_id)
            else:
                # Если нет метода retry, используем отмену как fallback
                debug(
                    f"Провайдер {provider_name} не поддерживает retry, используем отмену"
                )
                return await provider.cancel_activation(activation_id)
        except Exception as e:
            error(f"Ошибка запроса повторного кода от {provider_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_provider_stats(self) -> Dict[str, Any]:
        """
        Получить статистику по провайдерам

        Returns:
            Статистика провайдеров
        """
        await self.reload_providers()

        total_providers = len(self.provider_configs)
        active_providers = len(self.providers)

        firefox_count = sum(
            1
            for config in self.provider_configs
            if config.get("api_format") == "firefox_api"
        )
        smsactivate_count = sum(
            1
            for config in self.provider_configs
            if config.get("api_format") == "smsactivate_api"
        )

        return {
            "total_providers": total_providers,
            "active_providers": active_providers,
            "firefox_providers": firefox_count,
            "smsactivate_providers": smsactivate_count,
            "last_reload": self.last_reload_time,
        }

    def get_supported_api_formats(self) -> Dict[str, str]:
        """
        Получить поддерживаемые форматы API

        Returns:
            Словарь формат -> описание
        """
        return self.factory.get_supported_api_formats()


# Глобальный экземпляр менеджера
dynamic_manager = DynamicProviderManager()
