"""
API эндпоинты для SMS активации
"""
from __future__ import annotations

from typing import Any, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_database_session
from app.core.logger import critical, get_logger
from app.models import User
from app.services import ActivationService, PriceService, UserService
from app.utils.request_utils import get_client_ip

logger = get_logger(__name__)

# Создаем роутер для публичного API
router = APIRouter()


@router.get("/handler_api.php")
async def sms_activate_api(
    request: Request,
    api_key: str = Query(..., description="API ключ пользователя"),
    action: str = Query(..., description="Действие API"),
    service: Optional[str] = Query(None, description="Код сервиса"),
    country: Optional[int] = Query(None, description="Код страны"),
    id: Optional[str] = Query(None, description="ID активации"),
    status: Optional[int] = Query(None, description="Статус для установки"),
    # Дополнительные параметры для совместимости
    operator: Optional[str] = Query(None, description="Оператор связи"),
    operators: Optional[str] = Query(
        None, description="Операторы связи (альтернативное название)"
    ),
    ref: Optional[str] = Query(None, description="Реферальный код"),
    maxPrice: Optional[float] = Query(None, description="Максимальная цена"),
    verification: Optional[bool] = Query(None, description="Верификация"),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Основной эндпоинт нашего SMS API

    Поддерживаемые действия:
    - getBalance: получение баланса
    - getNumber: заказ номера (обязательно: service, опционально: country)
    - getStatus: получение статуса активации (обязательно: id)
    - setStatus: установка статуса активации (обязательно: id, status)
    - getPrices: получение цен и доступности (опционально: country)
    - getNumbersStatus: получение количества доступных номеров (опционально: country)

    Дополнительные параметры:
    - operator: оператор связи
    - maxPrice, verification, ref: дополнительные параметры

    Пример: /stubs/handler_api.php?api_key=KEY&action=getNumber&service=tg&country=12
    """
    # Получаем IP-адрес клиента
    client_ip = get_client_ip(request)

    # Получаем пользователя по API ключу
    user_service = UserService(db)
    user = await user_service.get_user_by_api_key(api_key)

    if not user:
        return Response(content="BAD_KEY", media_type="text/plain")

    if not user.is_active:
        return Response(content="BANNED", media_type="text/plain")

    try:
        if action == "getBalance":
            return await handle_get_balance(user, db)

        elif action == "getNumber":
            if not service:
                return Response(content="BAD_SERVICE", media_type="text/plain")
            return await handle_get_number(
                user,
                service,
                country,
                operator,
                operators,
                maxPrice,
                verification,
                client_ip,
                db,
            )

        elif action == "getStatus":
            if not id:
                return Response(content="NO_ACTIVATION", media_type="text/plain")
            return await handle_get_status(user, id, client_ip, db)

        elif action == "setStatus":
            if not id or status is None:
                return Response(content="NO_ACTIVATION", media_type="text/plain")
            return await handle_set_status(user, id, status, client_ip, db)

        elif action == "getPrices":
            return await handle_get_prices(country, db)

        elif action == "getNumbersStatus":
            return await handle_get_numbers_status(country, db)

        else:
            return Response(content="BAD_ACTION", media_type="text/plain")

    except Exception as e:
        # Логируем ошибку и возвращаем общую ошибку
        critical(
            f"Ошибка API: действие={action}, пользователь={user.id}, ошибка={e}",
            exc_info=True,
        )
        return Response(content="ERROR", media_type="text/plain")


async def handle_get_balance(user: User, db: AsyncSession) -> Response:
    """Обработка запроса баланса"""
    # Возвращаем баланс пользователя
    return Response(content=f"ACCESS_BALANCE:{user.balance}", media_type="text/plain")


async def handle_get_number(
    user: User,
    service: str,
    country: Optional[int],
    operator: Optional[str],
    operators: Optional[str],
    max_price: Optional[float],
    verification: Optional[bool],
    client_ip: str,
    db: AsyncSession,
) -> Response:
    """Обработка запроса номера"""
    try:
        # Определяем итоговый оператор - используем operators если он есть, иначе operator
        final_operator = operators if operators else operator

        # Логируем входящий запрос
        logger.info(
            f"Запрос номера: пользователь={user.username}, сервис={service}, страна={country}, оператор={final_operator}, maxPrice={max_price}"
        )

        activation_service = ActivationService(db)

        # Передаем параметры operator и max_price в сервис активации
        result = await activation_service.create_activation(
            user.id,
            service,
            country,
            client_ip,
            operator=final_operator,
            max_price=max_price,
        )

        # Логируем результат
        if result["success"]:
            logger.info(
                f"Номер успешно получен: пользователь={user.username}, ID активации={result['activation_id']}, номер={result['phone_number']}"
            )
            return Response(
                content=f"ACCESS_NUMBER:{result['activation_id']}:{result['phone_number']}",
                media_type="text/plain",
            )
        else:
            logger.info(
                f"Ошибка получения номера: пользователь={user.username}, ошибка={result['error']}"
            )
            return Response(content=result["error"], media_type="text/plain")
    except Exception as e:
        critical(
            f"Ошибка получения номера: пользователь={user.username}, сервис={service}, ошибка={e}",
            exc_info=True,
        )
        return Response(content="ERROR", media_type="text/plain")


async def handle_get_status(
    user: User, activation_id: str, client_ip: str, db: AsyncSession
) -> Response:
    """Обработка запроса статуса активации"""
    try:
        activation_service = ActivationService(db)
        result = await activation_service.get_activation_status(
            activation_id, user.id, client_ip
        )

        if not result["success"]:
            return Response(content=result["error"], media_type="text/plain")

        status = result["status"]
        if status == "STATUS_OK":
            return Response(
                content=f"STATUS_OK:{result['sms_code']}", media_type="text/plain"
            )
        elif status == "STATUS_WAIT_CODE":
            return Response(content="STATUS_WAIT_CODE", media_type="text/plain")
        elif status in ["STATUS_CANCEL", "STATUS_TIMEOUT"]:
            return Response(content="STATUS_CANCEL", media_type="text/plain")
        else:
            return Response(content="STATUS_WAIT_CODE", media_type="text/plain")
    except Exception as e:
        critical(f"Ошибка получения статуса: {e}", exc_info=True)
        return Response(content="ERROR", media_type="text/plain")


async def handle_set_status(
    user: User, activation_id: str, status: int, client_ip: str, db: AsyncSession
) -> Response:
    """Обработка установки статуса активации"""
    try:
        activation_service = ActivationService(db)

        if status == 8:  # Отмена активации
            result = await activation_service.cancel_activation(
                activation_id, user.id, client_ip
            )
            if result["success"]:
                return Response(content="ACCESS_CANCEL", media_type="text/plain")
            else:
                return Response(content=result["error"], media_type="text/plain")
        elif status == 1:  # Сообщить о готовности номера
            # Пока просто возвращаем успех
            return Response(content="ACCESS_READY", media_type="text/plain")
        elif status == 3:  # Запросить еще один код
            result = await activation_service.request_retry(
                activation_id, user.id, client_ip
            )
            if result["success"]:
                return Response(content="ACCESS_RETRY", media_type="text/plain")
            else:
                return Response(content=result["error"], media_type="text/plain")
        elif status == 6:  # Завершить активацию
            return Response(content="ACCESS_ACTIVATION", media_type="text/plain")
        else:
            return Response(content="BAD_STATUS", media_type="text/plain")
    except Exception as e:
        critical(f"Ошибка установки статуса: {e}", exc_info=True)
        return Response(content="ERROR", media_type="text/plain")


async def handle_get_prices(country: Optional[int], db: AsyncSession) -> Dict[str, Any]:
    """Обработка запроса цен"""
    try:
        price_service = PriceService(db)
        return await price_service.get_prices(country)
    except Exception as e:
        critical(f"Ошибка получения цен: {e}", exc_info=True)
        return {"error": "ERROR"}


async def handle_get_numbers_status(
    country: Optional[int], db: AsyncSession
) -> Dict[str, Any]:
    """Обработка запроса количества доступных номеров"""
    try:
        price_service = PriceService(db)
        return await price_service.get_numbers_status(country)
    except Exception as e:
        critical(f"Ошибка получения статуса номеров: {e}", exc_info=True)
        return {"error": "ERROR"}
