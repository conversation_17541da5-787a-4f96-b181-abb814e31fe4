"""
API эндпоинты для управления SMS провайдерами
"""
from __future__ import annotations

from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_database_session
from app.core.security import get_current_admin_by_header
from app.models.user import User
from app.schemas.sms_provider import (
    APIFormatsResponseSchema,
    ProviderCreateSchema,
    ProviderListResponseSchema,
    ProviderPrioritySchema,
    ProviderResponseSchema,
    ProviderStatsSchema,
    ProviderTestConnectionSchema,
    ProviderTestRequestSchema,
    ProviderTestResponseSchema,
    ProviderToggleSchema,
    ProviderUpdateSchema,
)
from app.services.provider_config_service import ProviderConfigService

router = APIRouter(prefix="/providers", tags=["Провайдеры"])


@router.get("/", response_model=ProviderListResponseSchema)
async def get_providers(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderListResponseSchema:
    """
    Получить список всех провайдеров

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        providers = await provider_service.get_all_providers()
        active_count = sum(1 for p in providers if p.is_active)

        return ProviderListResponseSchema(
            providers=[ProviderResponseSchema.model_validate(p) for p in providers],
            total_count=len(providers),
            active_count=active_count,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения списка провайдеров: {str(e)}",
        )


@router.get("/active", response_model=List[ProviderResponseSchema])
async def get_active_providers(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> List[ProviderResponseSchema]:
    """
    Получить список активных провайдеров

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        providers = await provider_service.get_active_providers()
        return [ProviderResponseSchema.model_validate(p) for p in providers]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения активных провайдеров: {str(e)}",
        )


@router.get("/{provider_id}", response_model=ProviderResponseSchema)
async def get_provider(
    provider_id: int,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderResponseSchema:
    """
    Получить провайдер по ID

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        provider = await provider_service.get_provider_by_id(provider_id)
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Провайдер с ID {provider_id} не найден",
            )
        return ProviderResponseSchema.model_validate(provider)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения провайдера: {str(e)}",
        )


@router.post(
    "/", response_model=ProviderResponseSchema, status_code=status.HTTP_201_CREATED
)
async def create_provider(
    config: ProviderCreateSchema,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderResponseSchema:
    """
    Создать новый провайдер

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        provider = await provider_service.create_provider(config, current_user.id)
        return ProviderResponseSchema.model_validate(provider)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания провайдера: {str(e)}",
        )


@router.put("/{provider_id}", response_model=ProviderResponseSchema)
async def update_provider(
    provider_id: int,
    config: ProviderUpdateSchema,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderResponseSchema:
    """
    Обновить провайдер

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        provider = await provider_service.update_provider(provider_id, config)
        return ProviderResponseSchema.model_validate(provider)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления провайдера: {str(e)}",
        )


@router.delete("/{provider_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_provider(
    provider_id: int,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Удалить провайдер

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        await provider_service.delete_provider(provider_id)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка удаления провайдера: {str(e)}",
        )


@router.post("/{provider_id}/test", response_model=ProviderTestResponseSchema)
async def test_provider(
    provider_id: int,
    test_config: ProviderTestRequestSchema = ProviderTestRequestSchema(),
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderTestResponseSchema:
    """
    Протестировать API провайдера

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        result = await provider_service.test_provider_api(provider_id)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка тестирования провайдера: {str(e)}",
        )


@router.patch("/{provider_id}/toggle", response_model=ProviderResponseSchema)
async def toggle_provider(
    provider_id: int,
    toggle_config: ProviderToggleSchema,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderResponseSchema:
    """
    Включить/отключить провайдер

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        provider = await provider_service.toggle_provider(
            provider_id, toggle_config.is_active
        )
        return ProviderResponseSchema.model_validate(provider)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка переключения провайдера: {str(e)}",
        )


@router.patch("/{provider_id}/priority", response_model=ProviderResponseSchema)
async def update_provider_priority(
    provider_id: int,
    priority_config: ProviderPrioritySchema,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderResponseSchema:
    """
    Изменить приоритет провайдера

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        provider = await provider_service.update_priority(
            provider_id, priority_config.priority
        )
        return ProviderResponseSchema.model_validate(provider)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка изменения приоритета: {str(e)}",
        )


@router.post("/reload-cache")
async def reload_providers_cache(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Перезагрузить кеш провайдеров

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        success = await provider_service.reload_providers_cache()
        return {
            "success": success,
            "message": "Кеш провайдеров перезагружен"
            if success
            else "Ошибка перезагрузки кеша",
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка перезагрузки кеша: {str(e)}",
        )


@router.get("/stats/summary", response_model=ProviderStatsSchema)
async def get_provider_stats(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderStatsSchema:
    """
    Получить статистику провайдеров

    Требует права администратора
    """
    try:
        from app.providers.dynamic_manager import dynamic_manager

        # Получаем статистику из менеджера
        stats = await dynamic_manager.get_provider_stats()

        # Получаем дополнительную статистику из БД
        provider_service = ProviderConfigService(db)
        all_providers = await provider_service.get_all_providers()

        successful_tests = sum(
            1 for p in all_providers if p.last_test_result == "SUCCESS"
        )
        failed_tests = sum(1 for p in all_providers if p.last_test_result == "FAILED")

        # Группируем по результатам тестов
        test_summary = {}
        for provider in all_providers:
            result = provider.last_test_result or "NOT_TESTED"
            test_summary[result] = test_summary.get(result, 0) + 1

        return ProviderStatsSchema(
            total_providers=stats["total_providers"],
            active_providers=stats["active_providers"],
            firefox_providers=stats["firefox_providers"],
            smsactivate_providers=stats["smsactivate_providers"],
            successful_tests=successful_tests,
            failed_tests=failed_tests,
            last_test_summary=test_summary,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статистики: {str(e)}",
        )


@router.post("/test-connection", response_model=ProviderTestResponseSchema)
async def test_provider_connection(
    provider_data: ProviderTestConnectionSchema,
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> ProviderTestResponseSchema:
    """
    Протестировать соединение с провайдером (без сохранения в БД)

    Требует права администратора
    """
    try:
        from app.core.logger import info

        info(
            f"Тестирование соединения с провайдером: {provider_data.api_url}, формат: {provider_data.api_format}"
        )
        info(f"Данные запроса: {provider_data.dict(exclude={'api_key', 'password'})}")

        provider_service = ProviderConfigService(db)

        # Создаем временный провайдер для тестирования
        # Конвертируем данные в формат, ожидаемый сервисом
        test_data = {
            "name": "test_connection",  # Временное имя для тестирования
            "display_name": "Test Connection",  # Временное отображаемое имя
            "api_url": provider_data.api_url,
            "api_format": provider_data.api_format,
            "timeout_seconds": provider_data.timeout_seconds,
            "priority": 100,
            "is_active": True,
            "max_requests_per_minute": 60,
            "settings": {},
        }

        # Добавляем учетные данные в зависимости от типа API
        if provider_data.api_format == "smsactivate_api" and provider_data.api_key:
            test_data["api_key"] = provider_data.api_key
        elif provider_data.api_format == "firefox_api":
            if provider_data.username:
                test_data["username"] = provider_data.username
            if provider_data.password:
                test_data["password"] = provider_data.password

        result = await provider_service.test_provider_connection(test_data)
        return result
    except Exception as e:
        from app.core.logger import error

        error(f"Ошибка тестирования соединения: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка тестирования соединения: {str(e)}",
        )


@router.post("/test-all")
async def test_all_providers(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Протестировать все активные провайдеры

    Требует права администратора
    """
    try:
        import traceback

        from app.core.logger import info

        info("Тестирование всех провайдеров")

        provider_service = ProviderConfigService(db)

        # Получаем всех активных провайдеров
        active_providers = await provider_service.get_active_providers()

        results = []
        for provider in active_providers:
            try:
                result = await provider_service.test_provider_api(provider.id)

                # Формируем подробный результат
                provider_result = {
                    "provider_id": provider.id,
                    "provider_name": provider.name,
                    "success": result.success,
                    "message": result.message,
                    "response_time": result.test_duration,
                }

                # Добавляем дополнительные детали если есть
                if result.details:
                    provider_result["api_response"] = result.details

                # Если есть ошибка, добавляем подробности
                if not result.success and hasattr(result, "details") and result.details:
                    # Извлекаем подробности ошибки из details
                    if isinstance(result.details, dict):
                        if "error" in result.details:
                            provider_result["error_details"] = result.details["error"]
                        if "response" in result.details:
                            provider_result["api_response"] = result.details["response"]
                        if "status_code" in result.details:
                            provider_result["status_code"] = result.details[
                                "status_code"
                            ]

                results.append(provider_result)

            except Exception as e:
                # Получаем подробную информацию об ошибке
                error_traceback = traceback.format_exc()
                error_details = f"{str(e)}\n\nПодробный стек трейс:\n{error_traceback}"

                results.append(
                    {
                        "provider_id": provider.id,
                        "provider_name": provider.name,
                        "success": False,
                        "message": f"Ошибка тестирования: {str(e)}",
                        "response_time": 0,
                        "error_details": error_details,
                        "exception_type": type(e).__name__,
                    }
                )

        success_count = sum(1 for r in results if r["success"])
        total_count = len(results)

        return {
            "results": results,
            "summary": {
                "total": total_count,
                "successful": success_count,
                "failed": total_count - success_count,
            },
        }
    except Exception as e:
        import traceback

        from app.core.logger import error

        error_traceback = traceback.format_exc()
        error(f"Ошибка тестирования всех провайдеров: {str(e)}\n{error_traceback}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка тестирования всех провайдеров: {str(e)}",
        )


@router.post("/reload")
async def reload_providers_cache_alias(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """
    Алиас для перезагрузки кеша провайдеров (совместимость с фронтендом)

    Требует права администратора
    """
    return await reload_providers_cache(current_user, db)


@router.get("/formats/supported", response_model=APIFormatsResponseSchema)
async def get_supported_api_formats(
    current_user: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> APIFormatsResponseSchema:
    """
    Получить список поддерживаемых форматов API

    Требует права администратора
    """
    try:
        provider_service = ProviderConfigService(db)
        formats_dict = provider_service.get_api_formats()
        formats_list = [
            {"format": k, "description": v} for k, v in formats_dict.items()
        ]

        return APIFormatsResponseSchema(formats=formats_list)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения форматов API: {str(e)}",
        )
