"""
Административная панель для управления системой
"""
from __future__ import annotations

from decimal import Decimal
import os
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlalchemy import and_, case, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.database import get_database_session
from app.core.security import get_current_admin_by_header
from app.models import (
    Activation,
    Country,
    Log,
    Price,
    Service,
    Setting,
    Transaction,
    User,
)
from app.schemas import Activation as ActivationSchema
from app.schemas import Country as CountrySchema
from app.schemas import CountryCreate, CountryUpdate
from app.schemas import Log as LogSchema
from app.schemas import Price as PriceSchema
from app.schemas import PriceCreate, PriceUpdate
from app.schemas import Service as ServiceSchema
from app.schemas import ServiceCreate, ServiceUpdate
from app.schemas import SettingResponse as SettingSchema
from app.schemas import SettingUpdate
from app.schemas import Transaction as TransactionSchema
from app.schemas import User as UserSchema
from app.schemas import UserCreate, UserUpdate
from app.services import UserService
from app.utils.request_utils import get_client_ip
from app.utils.security_monitor import SecurityMonitor
from app.utils.user_helpers import get_user_or_404
from app.utils.validators import validate_positive_amount

# Создаем роутер для административной панели
router = APIRouter(tags=["Admin Panel"])


def update_env_file(settings_data: dict) -> None:
    """
    Обновляет .env файл с новыми настройками

    Args:
        settings_data: Словарь с настройками для записи в .env
    """
    env_file_path = ".env"

    # Маппинг полей базы данных на переменные окружения (только системные настройки)
    env_mapping = {
        "debug_mode": "DEBUG",
        "log_level": "LOG_LEVEL",
    }

    # Читаем существующий .env файл
    env_lines = []
    if os.path.exists(env_file_path):
        with open(env_file_path, "r", encoding="utf-8") as f:
            env_lines = f.readlines()

    # Создаем словарь для обновления
    updates = {}
    for db_field, env_var in env_mapping.items():
        if db_field in settings_data and settings_data[db_field] is not None:
            # Специальная обработка для debug_mode
            if db_field == "debug_mode":
                updates[env_var] = "True" if settings_data[db_field] else "False"
            else:
                updates[env_var] = str(settings_data[db_field])

    # Обновляем существующие строки или добавляем новые
    updated_vars = set()
    for i, line in enumerate(env_lines):
        line = line.strip()
        if "=" in line and not line.startswith("#"):
            var_name = line.split("=")[0]
            if var_name in updates:
                env_lines[i] = f"{var_name}={updates[var_name]}\n"
                updated_vars.add(var_name)

    # Добавляем новые переменные, которые не были найдены
    for env_var, value in updates.items():
        if env_var not in updated_vars:
            env_lines.append(f"{env_var}={value}\n")

    # Записываем обновленный файл
    with open(env_file_path, "w", encoding="utf-8") as f:
        f.writelines(env_lines)


@router.get("/users", response_model=List[UserSchema])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение списка пользователей"""
    result = await db.execute(
        select(User).offset(skip).limit(limit).order_by(User.created_at.desc())
    )
    return result.scalars().all()


@router.post("/users", response_model=UserSchema)
async def create_user(
    user_data: UserCreate,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Создание нового пользователя"""
    client_ip = get_client_ip(request)
    user_service = UserService(db)
    return await user_service.create_user(user_data, client_ip)


@router.put("/users/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Обновление пользователя"""
    client_ip = get_client_ip(request)
    user_service = UserService(db)
    user = await user_service.update_user(user_id, user_data, client_ip)

    if not user:
        raise HTTPException(status_code=404, detail="Пользователь не найден")

    return user


@router.post("/users/{user_id}/topup")
async def topup_user_balance(
    user_id: int,
    request_data: dict,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Пополнение баланса пользователя"""
    from app.core.logger import critical, info

    info(
        f"Запрос пополнения баланса пользователя {user_id} администратором {current_admin.username}"
    )
    info(f"Данные запроса: {request_data}")

    user_service = UserService(db)
    try:
        amount = validate_positive_amount(
            request_data.get("amount", 0), "Сумма пополнения"
        )
        info(f"Сумма пополнения: {amount}")

        comment = request_data.get(
            "comment", f"Пополнение администратором {current_admin.username}"
        )
        client_ip = get_client_ip(request)
        await user_service.topup_user_balance(user_id, amount, comment, client_ip)
        info(f"Баланс успешно пополнен для пользователя {user_id}")
        return {"success": True, "message": f"Баланс пополнен на {amount}"}
    except Exception as e:
        critical(f"Ошибка пополнения баланса: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/users/{user_id}/deduct")
async def deduct_user_balance(
    user_id: int,
    request_data: dict,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Списание баланса пользователя"""
    from app.core.logger import critical, info

    info(
        f"Запрос списания баланса пользователя {user_id} администратором {current_admin.username}"
    )
    info(f"Данные запроса: {request_data}")

    user_service = UserService(db)
    try:
        amount = validate_positive_amount(
            request_data.get("amount", 0), "Сумма списания"
        )
        info(f"Сумма списания: {amount}")

        comment = request_data.get(
            "comment", f"Списание администратором {current_admin.username}"
        )
        client_ip = get_client_ip(request)
        # Списываем баланс
        await user_service.deduct_user_balance(user_id, amount, comment, client_ip)
        info(f"Баланс успешно списан для пользователя {user_id}")
        return {"success": True, "message": f"Баланс списан на {amount}"}
    except Exception as e:
        critical(f"Ошибка списания баланса: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/users/{user_id}", response_model=UserSchema)
async def get_user(
    user_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение данных конкретного пользователя"""
    user = await get_user_or_404(user_id, db)
    return user


@router.get("/users/{user_id}/api-key")
async def get_user_api_key(
    user_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение API ключа пользователя"""
    user = await get_user_or_404(user_id, db)
    return {
        "api_key": user.api_key,
        "user_id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role,
    }


@router.post("/users/{user_id}/regenerate-api-key")
async def regenerate_user_api_key(
    user_id: int,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Генерация нового API ключа для пользователя"""
    from app.core.logger import critical, info

    client_ip = get_client_ip(request)
    info(
        f"Запрос генерации нового API ключа для пользователя {user_id} администратором {current_admin.username} с IP {client_ip}"
    )

    user_service = UserService(db)
    user = await get_user_or_404(user_id, db)

    try:
        # Генерируем новый API ключ
        old_api_key = user.api_key
        new_api_key = user_service._generate_api_key()
        user.api_key = new_api_key

        # Очищаем кеш авторизации если это администратор
        if user.role == "admin":
            from app.core.security import clear_admin_auth_cache

            clear_admin_auth_cache(old_api_key)
            clear_admin_auth_cache(new_api_key)

        # Создаем лог о смене API ключа
        log_entry = Log(
            user_id=user_id,
            action="API_KEY_REGENERATED",
            details=f"API ключ изменен администратором {current_admin.username}. Старый ключ: {old_api_key[:8]}...",
            ip_address=client_ip,
        )

        db.add(log_entry)
        await db.commit()
        await db.refresh(user)

        info(f"API ключ успешно сгенерирован для пользователя {user_id}")
        return {
            "success": True,
            "message": "API ключ успешно обновлен",
            "new_api_key": new_api_key,
        }
    except Exception as e:
        critical(f"Ошибка генерации API ключа: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/countries", response_model=List[CountrySchema])
async def get_countries(
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение списка стран"""
    result = await db.execute(select(Country).order_by(Country.name))
    return result.scalars().all()


@router.post("/countries", response_model=CountrySchema)
async def create_country(
    country_data: CountryCreate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Создание новой страны"""
    country = Country(**country_data.model_dump())
    db.add(country)
    await db.commit()
    await db.refresh(country)
    return country


@router.put("/countries/{country_id}", response_model=CountrySchema)
async def update_country(
    country_id: int,
    country_data: CountryUpdate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Обновление страны"""
    result = await db.execute(select(Country).where(Country.id == country_id))
    country = result.scalar_one_or_none()

    if not country:
        raise HTTPException(status_code=404, detail="Страна не найдена")

    update_data = country_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(country, field, value)

    await db.commit()
    await db.refresh(country)
    return country


@router.get("/services", response_model=List[ServiceSchema])
async def get_services(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение списка сервисов с поддержкой пагинации"""
    result = await db.execute(
        select(Service).offset(skip).limit(limit).order_by(Service.id.desc())
    )
    return result.scalars().all()


@router.post("/services", response_model=ServiceSchema)
async def create_service(
    service_data: ServiceCreate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Создание нового сервиса"""
    service = Service(**service_data.model_dump())
    db.add(service)
    await db.commit()
    await db.refresh(service)
    return service


@router.put("/services/{service_id}", response_model=ServiceSchema)
async def update_service(
    service_id: int,
    service_data: ServiceUpdate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Обновление сервиса"""
    result = await db.execute(select(Service).where(Service.id == service_id))
    service = result.scalar_one_or_none()

    if not service:
        raise HTTPException(status_code=404, detail="Сервис не найден")

    update_data = service_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(service, field, value)

    await db.commit()
    await db.refresh(service)
    return service


@router.delete("/services/{service_id}")
async def delete_service(
    service_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Удаление сервиса"""
    result = await db.execute(select(Service).where(Service.id == service_id))
    service = result.scalar_one_or_none()

    if not service:
        raise HTTPException(status_code=404, detail="Сервис не найден")

    # Проверяем, есть ли связанные цены или активации
    prices_count = await db.execute(
        select(func.count(Price.id)).where(Price.service_id == service_id)
    )
    if prices_count.scalar() > 0:
        raise HTTPException(
            status_code=400, detail="Нельзя удалить сервис, у которого есть цены"
        )

    activations_count = await db.execute(
        select(func.count(Activation.id)).where(Activation.service_id == service_id)
    )
    if activations_count.scalar() > 0:
        raise HTTPException(
            status_code=400, detail="Нельзя удалить сервис, у которого есть активации"
        )

    await db.delete(service)
    await db.commit()

    return {"message": "Сервис успешно удален"}


@router.delete("/countries/{country_id}")
async def delete_country(
    country_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Удаление страны"""
    result = await db.execute(select(Country).where(Country.id == country_id))
    country = result.scalar_one_or_none()

    if not country:
        raise HTTPException(status_code=404, detail="Страна не найдена")

    # Проверяем, есть ли связанные цены или активации
    prices_count = await db.execute(
        select(func.count(Price.id)).where(Price.country_id == country_id)
    )
    if prices_count.scalar() > 0:
        raise HTTPException(
            status_code=400, detail="Нельзя удалить страну, у которой есть цены"
        )

    activations_count = await db.execute(
        select(func.count(Activation.id)).where(Activation.country_id == country_id)
    )
    if activations_count.scalar() > 0:
        raise HTTPException(
            status_code=400, detail="Нельзя удалить страну, у которой есть активации"
        )

    await db.delete(country)
    await db.commit()

    return {"message": "Страна успешно удалена"}


@router.get("/prices", response_model=List[PriceSchema])
async def get_prices(
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение списка цен"""
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(Price)
        .options(selectinload(Price.country), selectinload(Price.service))
        .order_by(Price.id)
    )
    return result.scalars().all()


@router.post("/prices", response_model=PriceSchema)
async def create_price(
    price_data: PriceCreate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Создание новой цены"""
    from sqlalchemy.orm import selectinload

    price = Price(**price_data.model_dump())
    db.add(price)
    await db.commit()

    # Перезагружаем с связанными данными
    result = await db.execute(
        select(Price)
        .options(selectinload(Price.country), selectinload(Price.service))
        .where(Price.id == price.id)
    )
    return result.scalar_one()


@router.put("/prices/{price_id}", response_model=PriceSchema)
async def update_price(
    price_id: int,
    price_data: PriceUpdate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Обновление цены"""
    from sqlalchemy.orm import selectinload

    result = await db.execute(select(Price).where(Price.id == price_id))
    price = result.scalar_one_or_none()

    if not price:
        raise HTTPException(status_code=404, detail="Цена не найдена")

    update_data = price_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(price, field, value)

    await db.commit()

    # Перезагружаем с связанными данными
    result = await db.execute(
        select(Price)
        .options(selectinload(Price.country), selectinload(Price.service))
        .where(Price.id == price_id)
    )
    return result.scalar_one()


@router.delete("/prices/{price_id}")
async def delete_price(
    price_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Удаление цены"""
    result = await db.execute(select(Price).where(Price.id == price_id))
    price = result.scalar_one_or_none()

    if not price:
        raise HTTPException(status_code=404, detail="Цена не найдена")

    await db.delete(price)
    await db.commit()

    return {"message": "Цена успешно удалена"}


@router.get("/settings", response_model=SettingSchema)
async def get_settings(
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение настроек системы"""
    result = await db.execute(select(Setting).limit(1))
    settings = result.scalar_one_or_none()

    if not settings:
        # Создаем настройки по умолчанию
        settings = Setting()
        db.add(settings)
        await db.commit()
        await db.refresh(settings)

    return settings


@router.put("/settings", response_model=SettingSchema)
async def update_settings(
    settings_data: SettingUpdate,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Обновление настроек системы"""
    from app.core.logger import info, warning

    result = await db.execute(select(Setting).limit(1))
    settings = result.scalar_one_or_none()

    if not settings:
        settings = Setting()
        db.add(settings)

    update_data = settings_data.model_dump(exclude_unset=True)

    # Логируем изменения настроек
    info(
        f"Администратор {current_admin.username} обновляет настройки системы: {list(update_data.keys())}"
    )

    for field, value in update_data.items():
        setattr(settings, field, value)

    await db.commit()
    await db.refresh(settings)

    # Обновляем файл .env с новыми настройками переменных окружения
    try:
        update_env_file(update_data)
        info("Файл .env успешно обновлен с новыми настройками")
    except Exception as e:
        warning(f"Ошибка обновления файла .env: {e}")
        # Не прерываем выполнение, так как настройки уже сохранены в БД

    return settings


@router.get("/activations")
async def get_activations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    status: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение списка активаций с пагинацией"""
    # Запрос для подсчета общего количества записей
    count_query = select(func.count(Activation.id))
    if status:
        count_query = count_query.where(Activation.status == status)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Запрос для получения данных
    query = (
        select(Activation)
        .options(
            selectinload(Activation.country),
            selectinload(Activation.service),
            selectinload(Activation.user),
        )
        .offset(skip)
        .limit(limit)
        .order_by(Activation.ordered_at.desc())
    )

    if status:
        query = query.where(Activation.status == status)

    result = await db.execute(query)
    activations = result.scalars().all()

    # Возвращаем объект с данными и информацией о пагинации
    return {
        "data": [
            ActivationSchema.model_validate(activation) for activation in activations
        ],
        "total": total,
        "skip": skip,
        "limit": limit,
        "pages": (total + limit - 1) // limit if limit > 0 else 1,
    }


@router.get("/statistics")
async def get_statistics(
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """Получение расширенной статистики системы"""
    from datetime import datetime, timedelta

    # Общая статистика активаций
    total_activations = await db.execute(select(func.count(Activation.id)))
    total_activations = total_activations.scalar()

    # Успешные активации
    successful_activations = await db.execute(
        select(func.count(Activation.id)).where(Activation.status == "SMS_RECEIVED")
    )
    successful_activations = successful_activations.scalar()

    # Отмененные активации
    canceled_activations = await db.execute(
        select(func.count(Activation.id)).where(
            Activation.status.in_(["CANCELED", "TIMEOUT"])
        )
    )
    canceled_activations = canceled_activations.scalar()

    # Активные активации (ожидающие SMS)
    active_activations = await db.execute(
        select(func.count(Activation.id)).where(Activation.status == "WAIT_SMS")
    )
    active_activations = active_activations.scalar()

    # Общий баланс пользователей
    total_balance = await db.execute(select(func.sum(User.balance)))
    total_balance = total_balance.scalar() or 0

    # Доходы от активаций
    total_revenue = await db.execute(
        select(func.sum(Activation.cost)).where(
            and_(Activation.charged == True, Activation.status == "SMS_RECEIVED")
        )
    )
    total_revenue = total_revenue.scalar() or 0

    # Общее количество пользователей
    total_users = await db.execute(select(func.count(User.id)))
    total_users = total_users.scalar()

    # Активные пользователи (заходили за последние 30 дней)
    thirty_days_ago = datetime.now() - timedelta(days=30)
    active_users = await db.execute(
        select(func.count(User.id)).where(User.last_login >= thirty_days_ago)
    )
    active_users = active_users.scalar()

    # Новые пользователи за последние 7 дней
    seven_days_ago = datetime.now() - timedelta(days=7)
    new_users_week = await db.execute(
        select(func.count(User.id)).where(User.created_at >= seven_days_ago)
    )
    new_users_week = new_users_week.scalar()

    # Активации за последние 24 часа
    yesterday = datetime.now() - timedelta(days=1)
    activations_today = await db.execute(
        select(func.count(Activation.id)).where(Activation.ordered_at >= yesterday)
    )
    activations_today = activations_today.scalar()

    # Топ сервисов
    top_services = await db.execute(
        select(Service.name, func.count(Activation.id).label("count"))
        .join(Activation, Service.id == Activation.service_id)
        .group_by(Service.name)
        .order_by(func.count(Activation.id).desc())
        .limit(5)
    )
    top_services = [{"service": row[0], "count": row[1]} for row in top_services.all()]

    # Статистика по провайдерам
    provider_stats = await db.execute(
        select(
            Activation.provider,
            func.count(Activation.id).label("total"),
            func.sum(case((Activation.status == "SMS_RECEIVED", 1), else_=0)).label(
                "successful"
            ),
        ).group_by(Activation.provider)
    )
    provider_stats = [
        {
            "provider": row[0],
            "total": row[1],
            "successful": row[2] or 0,
            "success_rate": round((row[2] or 0) / row[1] * 100, 2) if row[1] > 0 else 0,
        }
        for row in provider_stats.all()
    ]

    # Общий процент успеха
    success_rate = (
        (successful_activations / total_activations * 100)
        if total_activations > 0
        else 0
    )

    return {
        "total_users": total_users,
        "active_users": active_users,
        "new_users_week": new_users_week,
        "total_activations": total_activations,
        "activations_today": activations_today,
        "successful_activations": successful_activations,
        "canceled_activations": canceled_activations,
        "active_activations": active_activations,
        "success_rate": round(success_rate, 2),
        "total_balance": float(total_balance),
        "total_revenue": float(total_revenue),
        "top_services": top_services,
        "provider_stats": provider_stats,
    }


@router.get("/logs")
async def get_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    action: Optional[str] = Query(None),
    user_id: Optional[int] = Query(None),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение логов системы с информацией о пользователях и пагинацией"""
    # Запрос для подсчета общего количества записей
    count_query = select(func.count(Log.id))

    if action:
        count_query = count_query.where(Log.action == action)
    if user_id:
        count_query = count_query.where(Log.user_id == user_id)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Запрос для получения данных
    query = select(Log).options(selectinload(Log.user)).order_by(Log.timestamp.desc())

    if action:
        query = query.where(Log.action == action)

    if user_id:
        query = query.where(Log.user_id == user_id)

    query = query.offset(skip).limit(limit)

    result = await db.execute(query)
    logs = result.scalars().all()

    # Возвращаем объект с данными и информацией о пагинации
    return {
        "data": [LogSchema.model_validate(log) for log in logs],
        "total": total,
        "skip": skip,
        "limit": limit,
        "pages": (total + limit - 1) // limit if limit > 0 else 1,
    }


@router.get("/transactions")
async def get_transactions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, le=1000),
    user_id: Optional[int] = Query(None),
    transaction_type: Optional[str] = Query(None),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Получение транзакций с информацией о пользователях и пагинацией"""
    # Запрос для подсчета общего количества записей
    count_query = select(func.count(Transaction.id))

    if user_id:
        count_query = count_query.where(Transaction.user_id == user_id)
    if transaction_type:
        count_query = count_query.where(Transaction.type == transaction_type)

    total_result = await db.execute(count_query)
    total = total_result.scalar()

    # Запрос для получения данных
    query = (
        select(Transaction)
        .options(selectinload(Transaction.user))
        .order_by(Transaction.timestamp.desc())
    )

    if user_id:
        query = query.where(Transaction.user_id == user_id)

    if transaction_type:
        query = query.where(Transaction.type == transaction_type)

    query = query.offset(skip).limit(limit)

    result = await db.execute(query)
    transactions = result.scalars().all()

    # Возвращаем объект с данными и информацией о пагинации
    return {
        "data": [
            TransactionSchema.model_validate(transaction)
            for transaction in transactions
        ],
        "total": total,
        "skip": skip,
        "limit": limit,
        "pages": (total + limit - 1) // limit if limit > 0 else 1,
    }


# Эндпоинты для мониторинга безопасности
@router.get("/security/health-report")
async def get_security_health_report(
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """Получение отчета о состоянии безопасности системы"""
    monitor = SecurityMonitor(db)
    return await monitor.get_system_health_report()


@router.get("/security/user-analysis/{user_id}")
async def analyze_user_security(
    user_id: int,
    time_window_minutes: int = Query(10, ge=1, le=60),
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """Анализ подозрительной активности пользователя"""
    monitor = SecurityMonitor(db)

    balance_analysis = await monitor.check_suspicious_balance_activity(
        user_id, time_window_minutes
    )
    concurrent_analysis = await monitor.check_concurrent_activations(user_id)

    return {
        "user_id": user_id,
        "balance_activity": balance_analysis,
        "concurrent_activations": concurrent_analysis,
    }


@router.get("/security/balance-audit/{user_id}")
async def audit_user_balance(
    user_id: int,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
) -> Dict[str, Any]:
    """Аудит целостности баланса пользователя"""
    monitor = SecurityMonitor(db)
    return await monitor.audit_balance_integrity(user_id)


@router.post("/security/log-event")
async def log_security_event(
    request_data: dict,
    request: Request,
    current_admin: User = Depends(get_current_admin_by_header),
    db: AsyncSession = Depends(get_database_session),
):
    """Ручное логирование события безопасности"""
    monitor = SecurityMonitor(db)

    event_type = request_data.get("event_type", "MANUAL_EVENT")
    details = request_data.get("details", "")
    user_id = request_data.get("user_id")
    severity = request_data.get("severity", "INFO")
    ip_address = get_client_ip(request)

    await monitor.log_security_event(event_type, details, user_id, severity, ip_address)

    return {"success": True, "message": "Событие безопасности зарегистрировано"}
