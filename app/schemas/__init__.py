"""
Pydantic схемы для валидации данных
"""

from __future__ import annotations

from .activation import Activation, ActivationCreate, ActivationUpdate
from .api import NumberRequest, SetStatusRequest, SMSActivateResponse, StatusRequest
from .country import Country, CountryCreate, CountryUpdate
from .log import Log, LogCreate
from .price import Price, PriceCreate, PriceUpdate
from .service import Service, ServiceCreate, ServiceUpdate
from .setting import SettingResponse, SettingUpdate
from .transaction import Transaction, TransactionCreate

# Импорты всех схем для удобства использования
from .user import User, UserCreate, UserUpdate

# Экспортируем все схемы
__all__ = [
    # Пользователи
    "User",
    "UserCreate",
    "UserUpdate",
    # Страны
    "Country",
    "CountryCreate",
    "CountryUpdate",
    # Сервисы
    "Service",
    "ServiceCreate",
    "ServiceUpdate",
    # Цены
    "Price",
    "PriceCreate",
    "PriceUpdate",
    # Активации
    "Activation",
    "ActivationCreate",
    "ActivationUpdate",
    # Транзакции
    "Transaction",
    "TransactionCreate",
    # Логи
    "Log",
    "LogCreate",
    # Настройки
    "SettingResponse",
    "SettingUpdate",
    # API
    "SMSActivateResponse",
    "NumberRequest",
    "StatusRequest",
    "SetStatusRequest",
]
