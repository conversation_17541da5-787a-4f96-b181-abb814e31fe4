"""
Pydantic схемы для настроек системы
"""
from __future__ import annotations

from typing import Optional

from pydantic import BaseModel, Field


class SettingBase(BaseModel):
    """Базовая схема настроек"""

    # SMS провайдеры - настройки перенесены в таблицу sms_providers
    # Используйте административную панель для управления провайдерами

    # Системные настройки
    debug_mode: Optional[bool] = Field(default=False, description="Режим отладки")
    log_level: Optional[str] = Field(default="INFO", description="Уровень логирования")


class SettingCreate(SettingBase):
    """Схема для создания настроек"""

    pass


class SettingUpdate(BaseModel):
    """Схема для обновления настроек"""

    # Системные настройки
    debug_mode: Optional[bool] = None
    log_level: Optional[str] = None


class SettingResponse(SettingBase):
    """Схема ответа с настройками"""

    id: int

    class Config:
        from_attributes = True


class EnvironmentSettingResponse(BaseModel):
    """Схема для переменных окружения (упрощенная)"""

    # SMS провайдеры управляются через административную панель
    # Здесь только системные настройки
    debug_mode: bool
    log_level: str

    class Config:
        from_attributes = True
