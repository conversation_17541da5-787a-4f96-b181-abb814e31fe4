"""
API схемы для совместимости с SMSActivate
"""
from __future__ import annotations

from typing import Optional

from pydantic import BaseModel


class SMSActivateResponse(BaseModel):
    """Базовый ответ в формате SMSActivate"""

    pass


class NumberRequest(BaseModel):
    """Запрос номера"""

    service: str
    country: Optional[int] = None


class StatusRequest(BaseModel):
    """Запрос статуса"""

    id: str


class SetStatusRequest(BaseModel):
    """Установка статуса"""

    id: str
    status: int
