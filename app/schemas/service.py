"""
Схемы для сервисов
"""
from __future__ import annotations

from typing import Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema, UpdateSchema


class ServiceBase(BaseModel):
    """Базовая схема сервиса"""

    code: str
    name: str
    is_active: bool = True


class ServiceCreate(ServiceBase, CreateSchema):
    """Схема создания сервиса"""

    pass


class ServiceUpdate(UpdateSchema):
    """Схема обновления сервиса"""

    code: Optional[str] = None
    name: Optional[str] = None
    is_active: Optional[bool] = None


class Service(ServiceBase, BaseSchema):
    """Схема сервиса для ответа"""

    id: int
