"""
Схемы для активаций
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema, UpdateSchema
from .country import Country
from .service import Service
from .user import User


class ActivationBase(BaseModel):
    """Базовая схема активации"""

    provider: str
    country_id: int
    service_id: int
    cost: Decimal


class ActivationCreate(ActivationBase, CreateSchema):
    """Схема создания активации"""

    user_id: int


class ActivationUpdate(UpdateSchema):
    """Схема обновления активации"""

    provider_ref: Optional[str] = None
    phone_number: Optional[str] = None
    status: Optional[str] = None
    sms_code: Optional[str] = None
    completed_at: Optional[datetime] = None
    charged: Optional[bool] = None


class Activation(ActivationBase, BaseSchema):
    """Схема активации для ответа"""

    id: int
    user_id: int
    provider_ref: Optional[str]
    phone_number: Optional[str]
    status: str
    sms_code: Optional[str]
    ordered_at: datetime
    completed_at: Optional[datetime]
    charged: bool
    country: Country
    service: Service
    user: User
