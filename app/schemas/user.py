"""
Схемы для пользователей
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict

from .base import BaseSchema, CreateSchema, UpdateSchema


class UserBase(BaseModel):
    """Базовая схема пользователя"""

    username: str
    email: Optional[str] = None
    role: str = "user"
    is_active: bool = True


class UserCreate(UserBase, CreateSchema):
    """Схема создания пользователя"""

    password: Optional[str] = None
    balance: Optional[Decimal] = Decimal("0")  # Начальный баланс пользователя


class UserUpdate(UpdateSchema):
    """Схема обновления пользователя"""

    username: Optional[str] = None
    email: Optional[str] = None
    balance: Optional[Decimal] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None


class User(UserBase, BaseSchema):
    """Схема пользователя для ответа"""

    id: int
    api_key: str
    balance: Decimal
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None  # Последний вход пользователя
