"""
Схемы для цен
"""
from __future__ import annotations

from decimal import Decimal
from typing import Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema, UpdateSchema
from .country import Country
from .service import Service


class PriceBase(BaseModel):
    """Базовая схема цены"""

    country_id: int
    service_id: int
    price: Decimal
    provider_price_firefox: Optional[Decimal] = None
    provider_price_smslive: Optional[Decimal] = None
    available: Optional[int] = None


class PriceCreate(PriceBase, CreateSchema):
    """Схема создания цены"""

    pass


class PriceUpdate(UpdateSchema):
    """Схема обновления цены"""

    price: Optional[Decimal] = None
    provider_price_firefox: Optional[Decimal] = None
    provider_price_smslive: Optional[Decimal] = None
    available: Optional[int] = None


class Price(PriceBase, BaseSchema):
    """Схема цены для ответа"""

    id: int
    country: Country
    service: Service
