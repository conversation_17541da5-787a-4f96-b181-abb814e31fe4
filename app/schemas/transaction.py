"""
Схемы для транзакций
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema

if TYPE_CHECKING:
    from .user import User


class TransactionBase(BaseModel):
    """Базовая схема транзакции"""

    amount: Decimal
    type: str
    comment: Optional[str] = None


class TransactionCreate(TransactionBase, CreateSchema):
    """Схема создания транзакции"""

    user_id: int
    activation_id: Optional[int] = None


class UserInfo(BaseModel):
    """Информация о пользователе для транзакций"""

    id: int
    username: str

    class Config:
        from_attributes = True


class Transaction(TransactionBase, BaseSchema):
    """Схема транзакции для ответа"""

    id: int
    user_id: int
    activation_id: Optional[int] = None
    timestamp: datetime
    user: Optional[UserInfo] = None

    class Config:
        from_attributes = True
