"""
Схемы для стран
"""
from __future__ import annotations

from typing import Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema, UpdateSchema


class CountryBase(BaseModel):
    """Базовая схема страны"""

    code: int
    name: str
    is_active: bool = True


class CountryCreate(CountryBase, CreateSchema):
    """Схема создания страны"""

    pass


class CountryUpdate(UpdateSchema):
    """Схема обновления страны"""

    code: Optional[int] = None
    name: Optional[str] = None
    is_active: Optional[bool] = None


class Country(CountryBase, BaseSchema):
    """Схема страны для ответа"""

    id: int
