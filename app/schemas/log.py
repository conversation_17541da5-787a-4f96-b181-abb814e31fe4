"""
Схемы для логов
"""
from __future__ import annotations

from datetime import datetime
from typing import TYPE_CHECKING, Optional

from pydantic import BaseModel

from .base import BaseSchema, CreateSchema

if TYPE_CHECKING:
    from .user import User


class LogCreate(CreateSchema):
    """Схема создания лога"""

    user_id: Optional[int] = None
    action: str
    details: Optional[str] = None
    ip_address: Optional[str] = None


class UserInfo(BaseModel):
    """Информация о пользователе для логов"""

    id: int
    username: str

    class Config:
        from_attributes = True


class Log(BaseSchema):
    """Схема лога для ответа"""

    id: int
    timestamp: datetime
    user_id: Optional[int]
    action: str
    details: Optional[str]
    ip_address: Optional[str]
    user: Optional[UserInfo] = None

    class Config:
        from_attributes = True
