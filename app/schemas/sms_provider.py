"""
Схемы для SMS провайдеров
"""
from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class APIFormat(str, Enum):
    """Форматы API провайдеров"""

    SMSACTIVATE = "smsactivate_api"  # SMSActivate и совместимые
    FIREFOX = "firefox_api"  # Firefox и совместимые


class ProviderStatus(str, Enum):
    """Статусы тестирования провайдеров"""

    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    TIMEOUT = "TIMEOUT"
    NOT_TESTED = "NOT_TESTED"


class ProviderBaseSchema(BaseModel):
    """Базовая схема провайдера"""

    name: str = Field(
        ..., min_length=2, max_length=50, description="Уникальное имя провайдера"
    )
    display_name: str = Field(
        ..., min_length=2, max_length=100, description="Отображаемое имя"
    )
    api_url: str = Field(..., description="URL API провайдера")
    api_format: APIFormat = Field(..., description="Формат API")
    priority: int = Field(
        default=100, ge=1, le=1000, description="Приоритет провайдера"
    )
    is_active: bool = Field(default=True, description="Активен ли провайдер")
    max_requests_per_minute: int = Field(
        default=60, ge=1, le=1000, description="Лимит запросов в минуту"
    )
    timeout_seconds: int = Field(
        default=30, ge=5, le=300, description="Таймаут запросов"
    )
    settings: Dict[str, Any] = Field(
        default_factory=dict, description="Дополнительные настройки"
    )


class ProviderCreateSchema(ProviderBaseSchema):
    """Схема создания провайдера"""

    # Поля для SMSActivate API
    api_key: Optional[str] = Field(None, description="API ключ для SMSActivate")

    # Поля для Firefox API
    username: Optional[str] = Field(None, description="Логин для Firefox API")
    password: Optional[str] = Field(None, description="Пароль для Firefox API")

    @validator("api_key")
    def validate_smsactivate_fields(cls, v, values):
        """Валидация полей для SMSActivate API"""
        if values.get("api_format") == APIFormat.SMSACTIVATE and not v:
            raise ValueError("API ключ обязателен для SMSActivate провайдеров")
        return v

    @validator("username")
    def validate_firefox_username(cls, v, values):
        """Валидация логина для Firefox API"""
        if values.get("api_format") == APIFormat.FIREFOX and not v:
            raise ValueError("Логин обязателен для Firefox провайдеров")
        return v

    @validator("password")
    def validate_firefox_password(cls, v, values):
        """Валидация пароля для Firefox API"""
        if values.get("api_format") == APIFormat.FIREFOX and not v:
            raise ValueError("Пароль обязателен для Firefox провайдеров")
        return v


class ProviderUpdateSchema(BaseModel):
    """Схема обновления провайдера"""

    display_name: Optional[str] = Field(None, min_length=2, max_length=100)
    api_url: Optional[str] = None
    api_key: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    priority: Optional[int] = Field(None, ge=1, le=1000)
    is_active: Optional[bool] = None
    max_requests_per_minute: Optional[int] = Field(None, ge=1, le=1000)
    timeout_seconds: Optional[int] = Field(None, ge=5, le=300)
    settings: Optional[Dict[str, Any]] = None


class ProviderTestConnectionSchema(BaseModel):
    """Схема для тестирования соединения с провайдером (без сохранения в БД)"""

    api_url: str = Field(..., description="URL API провайдера")
    api_format: APIFormat = Field(..., description="Формат API")

    # Поля для SMSActivate API
    api_key: Optional[str] = Field(None, description="API ключ для SMSActivate")

    # Поля для Firefox API
    username: Optional[str] = Field(None, description="Логин для Firefox API")
    password: Optional[str] = Field(None, description="Пароль для Firefox API")

    # Дополнительные настройки для тестирования
    timeout_seconds: int = Field(
        default=30, ge=5, le=300, description="Таймаут запросов"
    )

    @validator("api_key")
    def validate_smsactivate_fields(cls, v, values):
        """Валидация полей для SMSActivate API"""
        if values.get("api_format") == APIFormat.SMSACTIVATE and not v:
            raise ValueError("API ключ обязателен для SMSActivate провайдеров")
        return v

    @validator("username")
    def validate_firefox_username(cls, v, values):
        """Валидация логина для Firefox API"""
        if values.get("api_format") == APIFormat.FIREFOX and not v:
            raise ValueError("Логин обязателен для Firefox провайдеров")
        return v

    @validator("password")
    def validate_firefox_password(cls, v, values):
        """Валидация пароля для Firefox API"""
        if values.get("api_format") == APIFormat.FIREFOX and not v:
            raise ValueError("Пароль обязателен для Firefox провайдеров")
        return v


class ProviderResponseSchema(ProviderBaseSchema):
    """Схема ответа с данными провайдера"""

    id: int
    provider_type: str

    # Конфиденциальные данные для редактирования (только для админов)
    api_key: Optional[str] = Field(None, description="API ключ для SMSActivate")
    username: Optional[str] = Field(None, description="Логин для Firefox API")
    password: Optional[str] = Field(None, description="Пароль для Firefox API")

    last_test_at: Optional[datetime]
    last_test_result: Optional[ProviderStatus]
    test_error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    created_by_user_id: Optional[int]

    class Config:
        from_attributes = True


class ProviderListResponseSchema(BaseModel):
    """Схема ответа со списком провайдеров"""

    providers: List[ProviderResponseSchema]
    total_count: int
    active_count: int


class ProviderTestRequestSchema(BaseModel):
    """Схема запроса тестирования провайдера"""

    test_balance: bool = Field(
        default=True, description="Тестировать получение баланса"
    )
    test_services: bool = Field(
        default=False, description="Тестировать получение списка сервисов"
    )


class ProviderTestResponseSchema(BaseModel):
    """Схема ответа тестирования провайдера"""

    success: bool
    status: ProviderStatus
    message: str
    details: Optional[Dict[str, Any]] = None
    test_duration: Optional[float] = None


class ProviderStatsSchema(BaseModel):
    """Схема статистики провайдеров"""

    total_providers: int
    active_providers: int
    firefox_providers: int
    smsactivate_providers: int
    successful_tests: int
    failed_tests: int
    last_test_summary: Dict[str, int]


class ProviderToggleSchema(BaseModel):
    """Схема переключения статуса провайдера"""

    is_active: bool


class ProviderPrioritySchema(BaseModel):
    """Схема изменения приоритета провайдера"""

    priority: int = Field(..., ge=1, le=1000)


class APIFormatsResponseSchema(BaseModel):
    """Схема ответа с поддерживаемыми форматами API"""

    formats: List[Dict[str, str]]
