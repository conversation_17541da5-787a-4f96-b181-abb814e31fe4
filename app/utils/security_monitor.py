"""
Утилиты для мониторинга безопасности и аудита операций
"""
from __future__ import annotations

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logger import critical, error, info
from app.models import Activation, Log, Transaction, User


class SecurityMonitor:
    """Монитор безопасности для отслеживания подозрительной активности"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def check_suspicious_balance_activity(
        self, user_id: int, time_window_minutes: int = 10
    ) -> Dict[str, Any]:
        """
        Проверка подозрительной активности с балансом пользователя

        Args:
            user_id: ID пользователя
            time_window_minutes: Временное окно для анализа в минутах

        Returns:
            Словарь с результатами анализа
        """
        time_threshold = datetime.utcnow() - timedelta(minutes=time_window_minutes)

        # Получаем транзакции пользователя за последние N минут
        result = await self.db.execute(
            select(Transaction)
            .where(
                and_(
                    Transaction.user_id == user_id,
                    Transaction.created_at >= time_threshold,
                )
            )
            .order_by(desc(Transaction.created_at))
        )
        transactions = result.scalars().all()

        # Анализируем активность
        analysis = {
            "user_id": user_id,
            "time_window_minutes": time_window_minutes,
            "total_transactions": len(transactions),
            "suspicious_patterns": [],
            "risk_level": "LOW",
        }

        if len(transactions) > 20:  # Более 20 транзакций за 10 минут
            analysis["suspicious_patterns"].append("HIGH_TRANSACTION_FREQUENCY")
            analysis["risk_level"] = "HIGH"

        # Проверяем на частые попытки резервирования
        reserve_attempts = [t for t in transactions if t.type.value == "RESERVE"]
        if len(reserve_attempts) > 10:
            analysis["suspicious_patterns"].append("EXCESSIVE_RESERVE_ATTEMPTS")
            analysis["risk_level"] = "HIGH"

        # Проверяем на быстрые отмены
        refund_transactions = [t for t in transactions if t.type.value == "REFUND"]
        if len(refund_transactions) > 5:
            analysis["suspicious_patterns"].append("FREQUENT_CANCELLATIONS")
            if analysis["risk_level"] == "LOW":
                analysis["risk_level"] = "MEDIUM"

        # Проверяем на крупные суммы
        large_amounts = [t for t in transactions if abs(t.amount) > Decimal("1000")]
        if large_amounts:
            analysis["suspicious_patterns"].append("LARGE_AMOUNT_TRANSACTIONS")
            if analysis["risk_level"] == "LOW":
                analysis["risk_level"] = "MEDIUM"

        return analysis

    async def check_concurrent_activations(self, user_id: int) -> Dict[str, Any]:
        """
        Проверка на одновременные активации (возможные race conditions)

        Args:
            user_id: ID пользователя

        Returns:
            Словарь с результатами анализа
        """
        # Получаем активные активации пользователя
        result = await self.db.execute(
            select(Activation).where(
                and_(
                    Activation.user_id == user_id,
                    Activation.status.in_(["WAITING_SMS", "PENDING"]),
                )
            )
        )
        active_activations = result.scalars().all()

        analysis = {
            "user_id": user_id,
            "active_activations_count": len(active_activations),
            "suspicious": False,
            "details": [],
        }

        if len(active_activations) > 5:  # Более 5 одновременных активаций
            analysis["suspicious"] = True
            analysis["details"].append("TOO_MANY_CONCURRENT_ACTIVATIONS")

        # Проверяем на активации, созданные в одно время (возможные race conditions)
        if len(active_activations) > 1:
            creation_times = [a.created_at for a in active_activations]
            creation_times.sort()

            for i in range(1, len(creation_times)):
                time_diff = (creation_times[i] - creation_times[i - 1]).total_seconds()
                if time_diff < 1:  # Менее 1 секунды между созданием
                    analysis["suspicious"] = True
                    analysis["details"].append("SIMULTANEOUS_ACTIVATION_CREATION")
                    break

        return analysis

    async def audit_balance_integrity(self, user_id: int) -> Dict[str, Any]:
        """
        Аудит целостности баланса пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Словарь с результатами аудита
        """
        # Получаем пользователя
        result = await self.db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()

        if not user:
            return {"error": "USER_NOT_FOUND"}

        # Получаем все транзакции пользователя
        result = await self.db.execute(
            select(Transaction)
            .where(Transaction.user_id == user_id)
            .order_by(Transaction.created_at)
        )
        transactions = result.scalars().all()

        # Вычисляем баланс на основе транзакций
        calculated_balance = Decimal("0")
        calculated_reserved = Decimal("0")

        for transaction in transactions:
            if transaction.type.value in ["TOPUP", "INITIAL_BALANCE", "REFUND"]:
                calculated_balance += transaction.amount
            elif transaction.type.value in ["CHARGE", "DEDUCT"]:
                calculated_balance += transaction.amount  # amount уже отрицательный
            elif transaction.type.value == "RESERVE":
                calculated_reserved += transaction.amount
            # При CHARGE зарезервированные средства уменьшаются автоматически

        # Получаем активные резервирования
        result = await self.db.execute(
            select(func.sum(Transaction.amount)).where(
                and_(Transaction.user_id == user_id, Transaction.type == "RESERVE")
            )
        )
        total_reserved = result.scalar() or Decimal("0")

        result = await self.db.execute(
            select(func.sum(Transaction.amount)).where(
                and_(Transaction.user_id == user_id, Transaction.type == "CHARGE")
            )
        )
        total_charged = abs(result.scalar() or Decimal("0"))

        result = await self.db.execute(
            select(func.sum(Transaction.amount)).where(
                and_(Transaction.user_id == user_id, Transaction.type == "REFUND")
            )
        )
        total_refunded = result.scalar() or Decimal("0")

        # Вычисляем ожидаемый зарезервированный баланс
        expected_reserved = total_reserved - total_charged - total_refunded

        audit_result = {
            "user_id": user_id,
            "current_balance": user.balance,
            "current_reserved": user.reserved_balance,
            "calculated_balance": calculated_balance,
            "expected_reserved": expected_reserved,
            "balance_matches": user.balance == calculated_balance,
            "reserved_matches": user.reserved_balance == expected_reserved,
            "integrity_ok": True,
            "discrepancies": [],
        }

        # Проверяем расхождения
        if user.balance != calculated_balance:
            audit_result["integrity_ok"] = False
            audit_result["discrepancies"].append(
                {
                    "type": "BALANCE_MISMATCH",
                    "expected": calculated_balance,
                    "actual": user.balance,
                    "difference": user.balance - calculated_balance,
                }
            )

        if user.reserved_balance != expected_reserved:
            audit_result["integrity_ok"] = False
            audit_result["discrepancies"].append(
                {
                    "type": "RESERVED_BALANCE_MISMATCH",
                    "expected": expected_reserved,
                    "actual": user.reserved_balance,
                    "difference": user.reserved_balance - expected_reserved,
                }
            )

        # Проверяем базовые ограничения
        if user.balance < 0:
            audit_result["integrity_ok"] = False
            audit_result["discrepancies"].append(
                {"type": "NEGATIVE_BALANCE", "value": user.balance}
            )

        if user.reserved_balance < 0:
            audit_result["integrity_ok"] = False
            audit_result["discrepancies"].append(
                {"type": "NEGATIVE_RESERVED_BALANCE", "value": user.reserved_balance}
            )

        if user.balance < user.reserved_balance:
            audit_result["integrity_ok"] = False
            audit_result["discrepancies"].append(
                {
                    "type": "INSUFFICIENT_BALANCE_FOR_RESERVED",
                    "balance": user.balance,
                    "reserved": user.reserved_balance,
                }
            )

        return audit_result

    async def get_system_health_report(self) -> Dict[str, Any]:
        """
        Получение отчета о состоянии системы

        Returns:
            Словарь с отчетом о состоянии
        """
        # Получаем статистику за последний час
        time_threshold = datetime.utcnow() - timedelta(hours=1)

        # Количество транзакций
        result = await self.db.execute(
            select(func.count(Transaction.id)).where(
                Transaction.created_at >= time_threshold
            )
        )
        recent_transactions = result.scalar()

        # Количество ошибок в логах
        result = await self.db.execute(
            select(func.count(Log.id)).where(
                and_(
                    Log.created_at >= time_threshold,
                    Log.action.in_(["ERROR", "RETRY_OPERATION"]),
                )
            )
        )
        recent_errors = result.scalar()

        # Количество активных пользователей
        result = await self.db.execute(
            select(func.count(func.distinct(Transaction.user_id))).where(
                Transaction.created_at >= time_threshold
            )
        )
        active_users = result.scalar()

        # Проверяем пользователей с подозрительной активностью
        suspicious_users = []
        result = await self.db.execute(
            select(Transaction.user_id, func.count(Transaction.id).label("count"))
            .where(Transaction.created_at >= time_threshold)
            .group_by(Transaction.user_id)
            .having(func.count(Transaction.id) > 50)  # Более 50 транзакций за час
        )

        for user_id, count in result:
            suspicious_users.append({"user_id": user_id, "transaction_count": count})

        return {
            "timestamp": datetime.utcnow(),
            "time_window": "1 hour",
            "metrics": {
                "recent_transactions": recent_transactions,
                "recent_errors": recent_errors,
                "active_users": active_users,
                "suspicious_users_count": len(suspicious_users),
            },
            "suspicious_users": suspicious_users,
            "health_status": "HEALTHY"
            if recent_errors < 10
            else "WARNING"
            if recent_errors < 50
            else "CRITICAL",
        }

    async def log_security_event(
        self,
        event_type: str,
        details: str,
        user_id: Optional[int] = None,
        severity: str = "INFO",
        ip_address: Optional[str] = None,
    ):
        """
        Логирование события безопасности

        Args:
            event_type: Тип события
            details: Детали события
            user_id: ID пользователя (если применимо)
            severity: Уровень серьезности (INFO, WARNING, CRITICAL)
            ip_address: IP-адрес пользователя (если применимо)
        """
        log_entry = Log(
            user_id=user_id,
            action=f"SECURITY_{event_type}",
            details=details,
            ip_address=ip_address,
        )

        self.db.add(log_entry)
        await self.db.commit()

        # Дополнительное логирование в зависимости от серьезности
        if severity == "WARNING":
            error(f"Security event: {event_type} - {details}")
        elif severity == "CRITICAL":
            critical(f"Critical security event: {event_type} - {details}")
        else:
            info(f"Security event: {event_type} - {details}")


async def run_security_monitoring_task(db: AsyncSession):
    """
    Фоновая задача для мониторинга безопасности

    Args:
        db: Сессия базы данных
    """
    monitor = SecurityMonitor(db)

    while True:
        try:
            # Получаем отчет о состоянии системы
            health_report = await monitor.get_system_health_report()

            # Проверяем подозрительных пользователей
            for suspicious_user in health_report["suspicious_users"]:
                user_id = suspicious_user["user_id"]

                # Анализируем активность пользователя
                balance_analysis = await monitor.check_suspicious_balance_activity(
                    user_id
                )
                concurrent_analysis = await monitor.check_concurrent_activations(
                    user_id
                )

                if balance_analysis["risk_level"] in ["HIGH", "MEDIUM"]:
                    await monitor.log_security_event(
                        "SUSPICIOUS_BALANCE_ACTIVITY",
                        f"User {user_id}: {balance_analysis['suspicious_patterns']}",
                        user_id,
                        "WARNING"
                        if balance_analysis["risk_level"] == "MEDIUM"
                        else "CRITICAL",
                    )

                if concurrent_analysis["suspicious"]:
                    await monitor.log_security_event(
                        "SUSPICIOUS_ACTIVATION_PATTERN",
                        f"User {user_id}: {concurrent_analysis['details']}",
                        user_id,
                        "WARNING",
                    )

            # Логируем общее состояние системы
            if health_report["health_status"] != "HEALTHY":
                await monitor.log_security_event(
                    "SYSTEM_HEALTH_WARNING",
                    f"System health: {health_report['health_status']}, errors: {health_report['metrics']['recent_errors']}",
                    severity="WARNING"
                    if health_report["health_status"] == "WARNING"
                    else "CRITICAL",
                )

            # Ждем 5 минут до следующей проверки
            await asyncio.sleep(300)

        except Exception as e:
            critical(f"Error in security monitoring task: {e}")
            await asyncio.sleep(60)  # Ждем минуту при ошибке
