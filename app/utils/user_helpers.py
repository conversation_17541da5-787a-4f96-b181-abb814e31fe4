"""
Утилиты для работы с пользователями
"""
from __future__ import annotations

from typing import Optional

from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import User
from app.services import UserService


async def get_user_or_404(user_id: int, db: AsyncSession) -> User:
    """
    Получение пользователя по ID с выбросом 404 ошибки если не найден

    Args:
        user_id: ID пользователя
        db: Сессия базы данных

    Returns:
        Объект пользователя

    Raises:
        HTTPException: 404 если пользователь не найден
    """
    user_service = UserService(db)
    user = await user_service.get_user_by_id(user_id)

    if not user:
        raise HTTPException(status_code=404, detail="Пользователь не найден")

    return user


async def get_user_by_api_key_or_none(api_key: str, db: AsyncSession) -> Optional[User]:
    """
    Получение пользователя по API ключу без выброса ошибки

    Args:
        api_key: API ключ пользователя
        db: Сессия базы данных

    Returns:
        Объект пользователя или None если не найден
    """
    user_service = UserService(db)
    return await user_service.get_user_by_api_key(api_key)
