"""
Утилиты для парсинга SMS сообщений
"""
from __future__ import annotations

import re
from typing import Optional


def extract_code_from_sms(sms_text: str) -> Optional[str]:
    """
    Извлечение кода из текста SMS
    Ищет числовые коды длиной 4-8 цифр

    Args:
        sms_text: Текст SMS сообщения

    Returns:
        Найденный код или None если код не найден
    """
    if not sms_text:
        return None

    # Паттерны для поиска кодов
    patterns = [
        r"\b(\d{4,8})\b",  # 4-8 цифр как отдельное слово
        r"код[:\s]*(\d{4,8})",  # "код: 1234" или "код 1234"
        r"code[:\s]*(\d{4,8})",  # "code: 1234" или "code 1234"
        r"verification[:\s]*(\d{4,8})",  # "verification: 1234"
        r"(\d{4,8})",  # просто 4-8 цифр
    ]

    for pattern in patterns:
        match = re.search(pattern, sms_text, re.IGNORECASE)
        if match:
            return match.group(1)

    return None
