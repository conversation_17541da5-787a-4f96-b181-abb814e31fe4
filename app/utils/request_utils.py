"""
Утилиты для работы с HTTP-запросами
"""
from __future__ import annotations

from typing import Optional

from fastapi import Request


def get_client_ip(request: Request) -> str:
    """
    Получение IP-адреса клиента из HTTP-запроса

    Проверяет заголовки прокси и возвращает реальный IP-адрес клиента

    Args:
        request: HTTP-запрос FastAPI

    Returns:
        IP-адрес клиента
    """
    # Проверяем заголовки прокси в порядке приоритета
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For может содержать несколько IP через запятую
        # Первый IP - это оригинальный клиент
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip.strip()

    # Cloudflare заголовок
    cf_connecting_ip = request.headers.get("CF-Connecting-IP")
    if cf_connecting_ip:
        return cf_connecting_ip.strip()

    # Используем IP из соединения как fallback
    if request.client and request.client.host:
        return request.client.host

    return "unknown"


def get_user_agent(request: Request) -> Optional[str]:
    """
    Получение User-Agent из HTTP-запроса

    Args:
        request: HTTP-запрос FastAPI

    Returns:
        User-Agent строка или None
    """
    return request.headers.get("User-Agent")


def is_suspicious_request(request: Request) -> bool:
    """
    Проверка запроса на подозрительные паттерны

    Args:
        request: HTTP-запрос FastAPI

    Returns:
        True если запрос подозрительный
    """
    user_agent = get_user_agent(request)

    # Подозрительные User-Agent
    suspicious_agents = [
        "curl",
        "wget",
        "python-requests",
        "bot",
        "crawler",
        "spider",
        "scraper",
        "scanner",
    ]

    if user_agent:
        user_agent_lower = user_agent.lower()
        for suspicious in suspicious_agents:
            if suspicious in user_agent_lower:
                return True

    # Отсутствие User-Agent тоже подозрительно
    if not user_agent:
        return True

    # Проверяем подозрительные пути
    suspicious_paths = [
        "/admin",
        "/.env",
        "/config",
        "/wp-admin",
        "/phpmyadmin",
        "/api/v1/",
        "/swagger",
        "/docs",
        "/openapi",
    ]

    path = request.url.path.lower()
    for suspicious_path in suspicious_paths:
        if suspicious_path in path:
            return True

    return False
