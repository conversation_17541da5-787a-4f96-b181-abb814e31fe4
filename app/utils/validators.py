"""
Утилиты для валидации данных
"""
from __future__ import annotations

from decimal import Decimal
from typing import Union


def validate_positive_amount(
    amount: Union[Decimal, int, float, str], field_name: str = "Сумма"
) -> Decimal:
    """
    Валидация положительной суммы

    Args:
        amount: Сумма для валидации
        field_name: Название поля для сообщения об ошибке

    Returns:
        Валидная сумма в формате Decimal

    Raises:
        ValueError: При некорректной сумме
    """
    try:
        amount_decimal = Decimal(str(amount))
    except (ValueError, TypeError):
        raise ValueError(f"{field_name} должна быть числом")

    if amount_decimal <= 0:
        raise ValueError(f"{field_name} должна быть больше нуля")

    return amount_decimal


def validate_non_negative_amount(
    amount: Union[Decimal, int, float, str], field_name: str = "Сумма"
) -> Decimal:
    """
    Валидация неотрицательной суммы

    Args:
        amount: Сумма для валидации
        field_name: Название поля для сообщения об ошибке

    Returns:
        Валидная сумма в формате Decimal

    Raises:
        ValueError: При некорректной сумме
    """
    try:
        amount_decimal = Decimal(str(amount))
    except (ValueError, TypeError):
        raise ValueError(f"{field_name} должна быть числом")

    if amount_decimal < 0:
        raise ValueError(f"{field_name} не может быть отрицательной")

    return amount_decimal
