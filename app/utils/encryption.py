"""
Утилиты для шифрования учетных данных провайдеров
"""
from __future__ import annotations

import base64
import os
from typing import Optional

from cryptography.fernet import Fernet

from app.core.config import settings


class CredentialEncryption:
    """
    Класс для шифрования и расшифровки учетных данных провайдеров
    Использует симметричное шифрование Fernet
    """

    def __init__(self):
        """Инициализация с ключом шифрования"""
        # Получаем ключ из настроек или генерируем новый
        encryption_key = getattr(settings, "encryption_key", None)

        if not encryption_key:
            # Генерируем новый ключ если не задан
            encryption_key = Fernet.generate_key().decode()
            print(f"⚠️ ВНИМАНИЕ: Сгенерирован временный ключ шифрования")
            print("Для продакшена добавьте ENCRYPTION_KEY в переменные окружения")

        if isinstance(encryption_key, str):
            encryption_key = encryption_key.encode()

        self.cipher = Fernet(encryption_key)

    def encrypt(self, data: str) -> str:
        """
        Зашифровать строку

        Args:
            data: Строка для шифрования

        Returns:
            Зашифрованная строка в base64
        """
        if not data:
            return ""

        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """
        Расшифровать строку

        Args:
            encrypted_data: Зашифрованная строка в base64

        Returns:
            Расшифрованная строка
        """
        if not encrypted_data:
            return ""

        try:
            decoded_data = base64.b64decode(encrypted_data.encode())
            decrypted_data = self.cipher.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception:
            # Если не удалось расшифровать, возможно данные не зашифрованы
            return encrypted_data

    def is_encrypted(self, data: str) -> bool:
        """
        Проверить, зашифрованы ли данные

        Args:
            data: Строка для проверки

        Returns:
            True если данные зашифрованы
        """
        if not data:
            return False

        try:
            decoded_data = base64.b64decode(data.encode())
            self.cipher.decrypt(decoded_data)
            return True
        except Exception:
            return False


# Глобальный экземпляр для использования
encryption = CredentialEncryption()


def encrypt_api_key(api_key: str) -> str:
    """
    Зашифровать API ключ

    Args:
        api_key: API ключ для шифрования

    Returns:
        Зашифрованный API ключ
    """
    return encryption.encrypt(api_key)


def decrypt_api_key(encrypted_api_key: str) -> str:
    """
    Расшифровать API ключ

    Args:
        encrypted_api_key: Зашифрованный API ключ

    Returns:
        Расшифрованный API ключ
    """
    return encryption.decrypt(encrypted_api_key)


def encrypt_password(password: str) -> str:
    """
    Зашифровать пароль

    Args:
        password: Пароль для шифрования

    Returns:
        Зашифрованный пароль
    """
    return encryption.encrypt(password)


def decrypt_password(encrypted_password: str) -> str:
    """
    Расшифровать пароль

    Args:
        encrypted_password: Зашифрованный пароль

    Returns:
        Расшифрованный пароль
    """
    return encryption.decrypt(encrypted_password)


def mask_sensitive_data(data: str, visible_chars: int = 4) -> str:
    """
    Замаскировать чувствительные данные для логов

    Args:
        data: Данные для маскирования
        visible_chars: Количество видимых символов в начале

    Returns:
        Замаскированная строка
    """
    if not data or len(data) <= visible_chars:
        return "***"

    return data[:visible_chars] + "*" * (len(data) - visible_chars)
