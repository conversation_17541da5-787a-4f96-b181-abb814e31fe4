"""
Основная конфигурация приложения SMS прокси-сервиса
"""
from __future__ import annotations

from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Настройки приложения"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # Настройки приложения
    app_title: str = "SMS Proxy Service"
    app_description: str = "Прокси-сервис для работы с провайдерами виртуальных номеров"
    app_version: str = "1.0.0"
    debug: bool = False

    # Настройки сервера
    host: str = "0.0.0.0"
    port: int = 8000
    allowed_hosts: List[str] = ["*"]

    # Настройки базы данных
    database_url: str

    # Настройки безопасности
    secret_key: str
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    encryption_key: Optional[
        str
    ] = None  # Ключ для шифрования учетных данных провайдеров (опционально)

    # Административная панель
    admin_email: str = "<EMAIL>"

    # Логирование
    log_level: str = "INFO"

    # Общие настройки SMS системы
    sms_timeout: int = Field(
        default=30, description="Таймаут для SMS операций в секундах"
    )

    @property
    def effective_log_level(self) -> str:
        """Возвращает эффективный уровень логирования с учетом DEBUG режима"""
        if self.debug:
            return "DEBUG"
        return self.log_level


# Создаем экземпляр настроек
settings = Settings()
