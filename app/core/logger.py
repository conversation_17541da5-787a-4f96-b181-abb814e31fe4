"""
Единая система логирования для SMS Proxy Service
Поддерживает режимы: DEBUG, INFO, WARNING, CRITICAL
"""

from __future__ import annotations

from datetime import datetime
import logging
from pathlib import Path
import sys
from typing import Optional

import structlog


class SecurityLogFilter(logging.Filter):
    """Фильтр для исключения логов безопасности из консольного вывода"""

    def filter(self, record):
        # Исключаем логи от логгера SECURITY из консольного вывода
        return record.name != "SECURITY"


class SMSLogger:
    """
    Единый логгер для всего приложения
    Логи безопасности (SECURITY) записываются только в файлы:
    - DEBUG: детальная отладочная информация (файл)
    - INFO: общая информация о работе приложения (консоль + файл)
    - WARNING: предупреждения (консоль + файл)
    - CRITICAL: критические ошибки (консоль + файл + отдельный файл)
    - SECURITY: логи безопасности (только файлы, исключены из консоли)
    """

    _instance: Optional["SMSLogger"] = None
    _initialized: bool = False

    def __new__(cls) -> "SMSLogger":
        """Реализация паттерна Singleton"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Инициализация логгера"""
        if not self._initialized:
            self._setup_logging()
            self._initialized = True

    def _setup_logging(self):
        """Настройка системы логирования"""
        # Пытаемся создать директорию для логов
        log_dir = Path("logs")

        # Проверяем возможность записи в директорию логов
        logs_available = False
        try:
            log_dir.mkdir(exist_ok=True)
            # Проверяем права на запись
            test_file = log_dir / "test_write_permissions"
            test_file.touch()
            test_file.unlink()
            logs_available = True
        except (PermissionError, OSError) as e:
            # Убираем print сообщения - логируем только в файлы
            logs_available = False

        # Настройка форматирования
        log_format = "%(asctime)s | %(levelname)-8s | %(name)s | %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"

        # Создаем форматтер
        formatter = logging.Formatter(log_format, date_format)

        # Настройка корневого логгера
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)

        # Очищаем существующие обработчики
        root_logger.handlers.clear()

        # Консольный обработчик для основных логов (исключая SECURITY)
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)  # Показываем INFO и выше
        console_handler.setFormatter(formatter)
        # Добавляем фильтр для исключения логов безопасности
        console_handler.addFilter(SecurityLogFilter())
        root_logger.addHandler(console_handler)

        # Если файлы недоступны, создаем пустой обработчик только для SECURITY логов
        if not logs_available:
            # Создаем null handler для SECURITY логгера чтобы избежать ошибок
            from logging import NullHandler

            security_logger = logging.getLogger("SECURITY")
            security_logger.addHandler(NullHandler())

        # Файловые обработчики - только если есть права на запись
        if logs_available:
            try:
                # Файловый обработчик для всех логов
                file_handler = logging.FileHandler(
                    log_dir / f"sms_proxy_{datetime.now().strftime('%Y%m%d')}.log",
                    encoding="utf-8",
                )
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)

                # Файловый обработчик только для критических ошибок
                critical_handler = logging.FileHandler(
                    log_dir
                    / f"critical_errors_{datetime.now().strftime('%Y%m%d')}.log",
                    encoding="utf-8",
                )
                critical_handler.setLevel(logging.CRITICAL)
                critical_handler.setFormatter(formatter)
                root_logger.addHandler(critical_handler)

            except (PermissionError, OSError) as e:
                # Убираем print сообщение - ошибки должны логироваться только в файлы
                pass

        # Настройка structlog для совместимости
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

    def get_logger(self, name: str) -> logging.Logger:
        """
        Получить логгер для модуля

        Args:
            name: имя модуля (обычно __name__)

        Returns:
            Настроенный логгер
        """
        return logging.getLogger(name)

    def debug(self, message: str, **kwargs):
        """Логирование отладочной информации"""
        logger = logging.getLogger("SMS_PROXY")
        if kwargs:
            message = f"{message} | {kwargs}"
        logger.debug(message)

    def info(self, message: str, **kwargs):
        """Логирование общей информации"""
        logger = logging.getLogger("SMS_PROXY")
        if kwargs:
            message = f"{message} | {kwargs}"
        logger.info(message)

    def warning(self, message: str, **kwargs):
        """Логирование предупреждений"""
        logger = logging.getLogger("SMS_PROXY")
        if kwargs:
            message = f"{message} | {kwargs}"
        logger.warning(message)

    def critical(self, message: str, **kwargs):
        """Логирование критических ошибок"""
        logger = logging.getLogger("SMS_PROXY")
        if kwargs:
            message = f"{message} | {kwargs}"
        logger.critical(message)

    def error(self, message: str, **kwargs):
        """Логирование ошибок (алиас для critical)"""
        self.critical(message, **kwargs)


# Глобальный экземпляр логгера
sms_logger = SMSLogger()


# Функции для быстрого доступа к логированию
def get_logger(name: str = None) -> logging.Logger:
    """Получить логгер для модуля"""
    if name is None:
        name = "SMS_PROXY"
    return sms_logger.get_logger(name)


def debug(message: str, **kwargs):
    """Логирование отладочной информации"""
    sms_logger.debug(message, **kwargs)


def info(message: str, **kwargs):
    """Логирование общей информации"""
    sms_logger.info(message, **kwargs)


def warning(message: str, **kwargs):
    """Логирование предупреждений"""
    sms_logger.warning(message, **kwargs)


def critical(message: str, **kwargs):
    """Логирование критических ошибок"""
    sms_logger.critical(message, **kwargs)


def error(message: str, **kwargs):
    """Логирование ошибок"""
    sms_logger.error(message, **kwargs)
