"""
Middleware для защиты от DDoS и rate limiting
"""
from __future__ import annotations

import time
from typing import Dict, List

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logger import error, info
from app.utils.request_utils import get_client_ip, is_suspicious_request


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Middleware для ограничения скорости запросов и защиты от DDoS
    """

    def __init__(self, app, max_requests: int = 100, time_window: int = 60):
        super().__init__(app)
        self.max_requests = max_requests  # Максимум запросов
        self.time_window = time_window  # Временное окно в секундах
        self.requests: Dict[str, List[float]] = {}  # IP -> список времен запросов

    def _get_client_ip(self, request: Request) -> str:
        """Получение IP адреса клиента"""
        return get_client_ip(request)

    def _is_rate_limited(self, client_ip: str) -> bool:
        """
        Проверка превышения лимита запросов

        Args:
            client_ip: IP адрес клиента

        Returns:
            True если лимит превышен
        """
        current_time = time.time()

        # Получаем список запросов для IP
        if client_ip not in self.requests:
            self.requests[client_ip] = []

        client_requests = self.requests[client_ip]

        # Удаляем старые запросы (старше time_window секунд)
        client_requests[:] = [
            req_time
            for req_time in client_requests
            if current_time - req_time < self.time_window
        ]

        # Проверяем лимит
        if len(client_requests) >= self.max_requests:
            return True

        # Добавляем текущий запрос
        client_requests.append(current_time)
        return False

    def _get_remaining_requests(self, client_ip: str) -> int:
        """Получение количества оставшихся запросов"""
        if client_ip not in self.requests:
            return self.max_requests

        current_time = time.time()
        client_requests = self.requests[client_ip]

        # Считаем активные запросы
        active_requests = sum(
            1
            for req_time in client_requests
            if current_time - req_time < self.time_window
        )

        return max(0, self.max_requests - active_requests)

    async def dispatch(self, request: Request, call_next):
        """Обработка запроса с проверкой rate limit"""
        client_ip = self._get_client_ip(request)

        # Проверяем rate limit
        if self._is_rate_limited(client_ip):
            remaining = self._get_remaining_requests(client_ip)

            error(f"Rate limit exceeded for IP {client_ip}. Path: {request.url.path}")

            return JSONResponse(
                status_code=429,
                content={
                    "error": "RATE_LIMIT_EXCEEDED",
                    "message": f"Превышен лимит запросов. Максимум {self.max_requests} запросов за {self.time_window} секунд",
                    "remaining_requests": remaining,
                    "retry_after": self.time_window,
                },
                headers={
                    "Retry-After": str(self.time_window),
                    "X-RateLimit-Limit": str(self.max_requests),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(int(time.time()) + self.time_window),
                },
            )

        # Добавляем заголовки rate limit в ответ
        response = await call_next(request)
        remaining = self._get_remaining_requests(client_ip)

        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + self.time_window)

        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware для добавления заголовков безопасности
    """

    async def dispatch(self, request: Request, call_next):
        """Добавление заголовков безопасности"""
        response = await call_next(request)

        # Заголовки безопасности
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # Более гибкая CSP политика для документации и админ панели
        if request.url.path.startswith("/admin") or request.url.path in [
            "/docs",
            "/redoc",
        ]:
            # Разрешаем встроенные стили и скрипты для документации
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "font-src 'self' https://cdn.jsdelivr.net; "
                "img-src 'self' data: https:; "
                "connect-src 'self'"
            )
        else:
            # Строгая политика для остальных страниц
            response.headers["Content-Security-Policy"] = "default-src 'self'"

        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware для логирования подозрительных запросов
    """

    def __init__(self, app):
        super().__init__(app)
        # Паттерны для SQL инъекций
        self.sql_patterns = [
            "union select",
            "drop table",
            "delete from",
            "insert into",
            "update set",
            "' or 1=1",
            "' or '1'='1",
            "'; drop",
            "/*",
            "*/",
        ]

        # Паттерны для XSS атак
        self.xss_patterns = [
            "<script",
            "javascript:",
            "eval(",
            "alert(",
            "onload=",
            "onerror=",
            "onclick=",
            "onmouseover=",
        ]

        # Паттерны для path traversal
        self.path_patterns = [
            "../",
            "..\\",
            "/etc/passwd",
            "/proc/",
            "cmd.exe",
            "powershell.exe",
        ]

        # Исключения - пути которые не должны проверяться
        self.excluded_paths = [
            "/static/",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/admin",
            "/api/admin/",
        ]

    def _is_suspicious_request(self, request: Request) -> bool:
        """
        Проверка на подозрительные паттерны в запросе

        Args:
            request: HTTP запрос

        Returns:
            True если запрос подозрительный
        """
        # Используем общую утилиту для проверки подозрительных запросов
        if is_suspicious_request(request):
            return True

        url_str = str(request.url).lower()
        path = request.url.path.lower()

        # Пропускаем проверку для исключенных путей
        for excluded in self.excluded_paths:
            if path.startswith(excluded):
                return False

        # Объединяем все паттерны
        all_patterns = self.sql_patterns + self.xss_patterns + self.path_patterns

        # Проверяем URL на точные совпадения
        for pattern in all_patterns:
            if pattern in url_str:
                return True

        # Проверяем заголовки
        for header_name, header_value in request.headers.items():
            if header_value:
                header_lower = header_value.lower()
                for pattern in all_patterns:
                    if pattern in header_lower:
                        return True

        return False

    async def dispatch(self, request: Request, call_next):
        """Логирование подозрительных запросов"""
        client_ip = get_client_ip(request)

        # Проверяем на подозрительные паттерны
        if self._is_suspicious_request(request):
            # Используем уровень WARNING вместо CRITICAL для подозрительных запросов
            from app.core.logger import get_logger

            logger = get_logger("SECURITY")
            logger.warning(
                f"Suspicious request from IP {client_ip}: {request.method} {request.url}"
            )
            logger.warning(f"Headers: {dict(request.headers)}")

        # Убираем дублирование - логируем только важные API запросы
        # Исключаем административные запросы из обычного логирования
        if request.url.path.startswith("/stubs/") and not request.url.path.startswith(
            "/api/admin/"
        ):
            info(f"API request: {client_ip} {request.method} {request.url.path}")

        response = await call_next(request)
        return response
