"""
Настройка подключения к базе данных
"""
from __future__ import annotations

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase

from app.core.config import settings


class Base(DeclarativeBase):
    """Базовый класс для всех моделей"""

    pass


# Создаем асинхронный движок базы данных
engine = create_async_engine(settings.database_url, echo=False, future=True)

# Создаем фабрику сессий
async_session_maker = async_sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def get_database_session() -> AsyncSession:
    """
    Получение сессии базы данных для dependency injection
    """
    async with async_session_maker() as session:
        try:
            yield session
        finally:
            await session.close()


async def init_database():
    """
    Инициализация базы данных - создание всех таблиц
    """
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
