"""
Система безопасности и аутентификации
"""
from __future__ import annotations

from datetime import datetime, timedelta
import time
from typing import Optional

from fastapi import Depends, Header, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_database_session
from app.models import User
from app.services import UserService

# Контекст для хеширования паролей
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer схема для токенов
security = HTTPBearer()

# Кеш для авторизации администраторов (в памяти)
# В продакшене лучше использовать Redis
_admin_auth_cache = {}
_cache_ttl = 120  # 2 минуты кеширования


def _is_cache_valid(cache_entry: dict) -> bool:
    """Проверяет валидность записи в кеше"""
    return time.time() - cache_entry.get("timestamp", 0) < _cache_ttl


def _get_cached_admin(api_key: str) -> Optional[dict]:
    """Получает администратора из кеша"""
    if api_key in _admin_auth_cache:
        cache_entry = _admin_auth_cache[api_key]
        if _is_cache_valid(cache_entry):
            return cache_entry.get("user_data")
        else:
            # Удаляем устаревшую запись
            del _admin_auth_cache[api_key]
    return None


def _cache_admin(api_key: str, user_data: dict):
    """Кеширует данные администратора"""
    _admin_auth_cache[api_key] = {"user_data": user_data, "timestamp": time.time()}


def _clear_admin_cache(api_key: str = None):
    """Очищает кеш администратора"""
    if api_key:
        _admin_auth_cache.pop(api_key, None)
    else:
        _admin_auth_cache.clear()


def clear_admin_auth_cache(api_key: str = None):
    """Публичная функция для очистки кеша авторизации администратора"""
    _clear_admin_cache(api_key)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Создание JWT токена доступа

    Args:
        data: Данные для включения в токен
        expires_delta: Время жизни токена

    Returns:
        JWT токен
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.access_token_expire_minutes
        )

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.secret_key, algorithm=settings.algorithm
    )

    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Проверка пароля"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Хеширование пароля"""
    return pwd_context.hash(password)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_database_session),
) -> User:
    """
    Получение текущего пользователя по JWT токену
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.secret_key,
            algorithms=[settings.algorithm],
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user_service = UserService(db)
    user = await user_service.get_user_by_api_key(username)  # В токене храним API ключ

    if user is None:
        raise credentials_exception

    return user


async def get_current_admin_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Получение текущего пользователя с правами администратора
    """
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Insufficient permissions"
        )

    return current_user


async def authenticate_user_by_api_key(
    api_key: str, db: AsyncSession
) -> Optional[User]:
    """
    Аутентификация пользователя по API ключу

    Args:
        api_key: API ключ пользователя
        db: Сессия базы данных

    Returns:
        Объект пользователя или None
    """
    user_service = UserService(db)
    user = await user_service.get_user_by_api_key(api_key)

    if not user or not user.is_active:
        return None

    # Обновляем время последнего входа
    user.last_login = datetime.now()
    await db.commit()

    return user


async def get_current_admin_by_api_key(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_database_session),
) -> User:
    """
    Получение администратора по API ключу из Bearer токена
    Используется для админ панели
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API ключ не предоставлен",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Извлекаем API ключ из Bearer токена
    api_key = credentials.credentials

    # Аутентифицируем пользователя
    user = await authenticate_user_by_api_key(api_key, db)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверный API ключ",
        )

    # Проверяем права администратора
    if user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Недостаточно прав доступа"
        )

    return user


async def get_current_admin_by_header(
    x_api_key: str = Header(None, alias="X-API-Key"),
    db: AsyncSession = Depends(get_database_session),
) -> User:
    """
    Получение администратора по API ключу из заголовка X-API-Key
    Используется для админ панели с кешированием
    """
    from app.core.logger import debug, get_logger, info

    logger = get_logger(__name__)

    if not x_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API ключ не предоставлен",
        )

    # Проверяем кеш сначала
    cached_user_data = _get_cached_admin(x_api_key)
    if cached_user_data:
        # Создаем объект User из кешированных данных
        user = User(
            id=cached_user_data["id"],
            username=cached_user_data["username"],
            email=cached_user_data["email"],
            role=cached_user_data["role"],
            balance=cached_user_data["balance"],
            is_active=cached_user_data["is_active"],
            api_key=cached_user_data["api_key"],
        )
        return user

    # Если не в кеше, выполняем полную проверку
    info(f"Попытка авторизации администратора с API ключом: {x_api_key[:10]}...")

    # Аутентифицируем пользователя
    user = await authenticate_user_by_api_key(x_api_key, db)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверный API ключ",
        )

    # Проверяем права администратора
    if user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Недостаточно прав доступа"
        )

    # Кешируем данные администратора
    user_data = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role,
        "balance": float(user.balance),
        "is_active": user.is_active,
        "api_key": user.api_key,
    }
    _cache_admin(x_api_key, user_data)

    info(f"Успешная авторизация администратора: {user.username}")
    return user
