"""
Модель пользователя системы с валидацией баланса
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    Boolean,
    CheckConstraint,
    DateTime,
    Integer,
    Numeric,
    String,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates

from app.core.database import Base

if TYPE_CHECKING:
    from .activation import Activation
    from .log import Log
    from .sms_provider import SMSProvider
    from .transaction import Transaction


class User(Base):
    """Модель пользователя системы с валидацией баланса"""

    __tablename__ = "users"

    # Ограничения на уровне БД для обеспечения целостности данных
    __table_args__ = (
        CheckConstraint("balance >= 0", name="check_balance_non_negative"),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    username: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    email: Mapped[Optional[str]] = mapped_column(String(255))
    api_key: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    balance: Mapped[Decimal] = mapped_column(Numeric(10, 2), default=Decimal("0.00"))
    role: Mapped[str] = mapped_column(String(20), default="user")  # user, admin
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Связи
    activations: Mapped[list["Activation"]] = relationship(
        "Activation", back_populates="user"
    )
    transactions: Mapped[list["Transaction"]] = relationship(
        "Transaction", back_populates="user"
    )
    logs: Mapped[list["Log"]] = relationship("Log", back_populates="user")

    # Связь с SMS провайдерами (созданные пользователем)
    created_providers: Mapped[list["SMSProvider"]] = relationship(
        "SMSProvider", back_populates="created_by_user"
    )

    @validates("balance")
    def validate_balance(self, key, value):
        """Валидация баланса на уровне модели"""
        if value is None:
            return Decimal("0.00")

        if not isinstance(value, (Decimal, int, float)):
            raise ValueError("Баланс должен быть числом")

        value = Decimal(str(value))

        if value < 0:
            raise ValueError("Баланс не может быть отрицательным")

        return value

    def __repr__(self) -> str:
        return (
            f"<User(id={self.id}, username='{self.username}', balance={self.balance})>"
        )
