"""
Модель SMS провайдера для динамического управления
"""
from __future__ import annotations

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from app.core.database import Base


class SMSProvider(Base):
    """
    Модель SMS провайдера
    Поддерживает динамическое добавление провайдеров разных типов API
    """

    __tablename__ = "sms_providers"

    # Основные поля
    id = Column(Integer, primary_key=True, autoincrement=True)

    # Основная информация
    name = Column(
        String(50), unique=True, nullable=False, comment="Уникальное имя провайдера"
    )
    display_name = Column(
        String(100), nullable=False, comment="Отображаемое имя для админки"
    )
    api_url = Column(String(500), nullable=False, comment="URL API провайдера")

    # Учетные данные (зашифрованы)
    api_key = Column(String(500), nullable=True, comment="API ключ для SMSActivate API")
    username = Column(String(100), nullable=True, comment="Логин для Firefox API")
    password = Column(String(500), nullable=True, comment="Пароль для Firefox API")

    # Тип провайдера и формат API
    provider_type = Column(String(30), nullable=False, comment="Тип провайдера")
    api_format = Column(
        String(20), nullable=False, comment="Формат API (firefox_api, smsactivate_api)"
    )

    # Настройки работы
    is_active = Column(Boolean, default=True, comment="Активен ли провайдер")
    priority = Column(Integer, default=100, comment="Приоритет (1 = высший)")
    max_requests_per_minute = Column(
        Integer, default=60, comment="Лимит запросов в минуту"
    )
    timeout_seconds = Column(Integer, default=30, comment="Таймаут запросов в секундах")

    # Дополнительные настройки в JSON
    settings = Column(JSONB, default={}, comment="Дополнительные настройки провайдера")

    # Статус тестирования
    last_test_at = Column(DateTime, nullable=True, comment="Последнее тестирование")
    last_test_result = Column(
        String(20), nullable=True, comment="Результат последнего теста"
    )
    test_error_message = Column(
        Text, nullable=True, comment="Сообщение об ошибке теста"
    )

    # Метаданные
    created_at = Column(DateTime, server_default=func.now(), comment="Дата создания")
    updated_at = Column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        comment="Дата обновления",
    )
    created_by_user_id = Column(
        Integer, ForeignKey("users.id"), nullable=True, comment="Создал пользователь"
    )

    # Отношения
    created_by_user = relationship("User", back_populates="created_providers")

    def __repr__(self):
        return f"<SMSProvider(name='{self.name}', api_format='{self.api_format}', active={self.is_active})>"

    class Config:
        """Конфигурация модели"""

        orm_mode = True
        use_enum_values = True
