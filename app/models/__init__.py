"""
Модели базы данных для SMS прокси-сервиса
"""

from __future__ import annotations

# Импорт базового класса
from app.core.database import Base

from .activation import Activation
from .country import Country
from .log import Log
from .price import Price
from .service import Service
from .setting import Setting
from .sms_provider import SMSProvider
from .transaction import Transaction, TransactionType

# Импорты всех моделей для удобства использования
from .user import User

# Экспортируем все модели
__all__ = [
    "Base",
    "User",
    "Country",
    "Service",
    "Price",
    "Activation",
    "Transaction",
    "TransactionType",
    "Log",
    "Setting",
    "SMSProvider",
]
