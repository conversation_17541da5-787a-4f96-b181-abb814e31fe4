"""
Модель страны
"""
from __future__ import annotations

from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

if TYPE_CHECKING:
    from .activation import Activation
    from .price import Price


class Country(Base):
    """Модель страны"""

    __tablename__ = "countries"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    code: Mapped[int] = mapped_column(
        Integer, unique=True, nullable=False
    )  # 0=Россия, 1=Украина
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Связи
    prices: Mapped[list["Price"]] = relationship("Price", back_populates="country")
    activations: Mapped[list["Activation"]] = relationship(
        "Activation", back_populates="country"
    )

    def __repr__(self) -> str:
        return f"<Country(id={self.id}, code={self.code}, name='{self.name}')>"
