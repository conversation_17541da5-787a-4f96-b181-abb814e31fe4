"""
Модель сервиса
"""
from __future__ import annotations

from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

if TYPE_CHECKING:
    from .activation import Activation
    from .price import Price


class Service(Base):
    """Модель сервиса"""

    __tablename__ = "services"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(
        String(10), unique=True, nullable=False
    )  # tg, wa, vk
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Связи
    prices: Mapped[list["Price"]] = relationship("Price", back_populates="service")
    activations: Mapped[list["Activation"]] = relationship(
        "Activation", back_populates="service"
    )

    def __repr__(self) -> str:
        return f"<Service(id={self.id}, code='{self.code}', name='{self.name}')>"
