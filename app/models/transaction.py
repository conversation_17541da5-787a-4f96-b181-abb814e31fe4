"""
Модель транзакций
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    BigInteger,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

if TYPE_CHECKING:
    from .activation import Activation
    from .user import User


class Transaction(Base):
    """Модель транзакций"""

    __tablename__ = "transactions"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.id"), nullable=False
    )
    amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    type: Mapped[str] = mapped_column(
        String(20), nullable=False
    )  # TOPUP, RESERVE, CHARGE, REFUND, DEDUCT
    activation_id: Mapped[Optional[int]] = mapped_column(
        BigInteger, <PERSON>Key("activations.id")
    )
    comment: Mapped[Optional[str]] = mapped_column(Text)
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    # Связи
    user: Mapped["User"] = relationship("User", back_populates="transactions")
    activation: Mapped[Optional["Activation"]] = relationship(
        "Activation", back_populates="transactions"
    )

    def __repr__(self) -> str:
        return f"<Transaction(id={self.id}, user_id={self.user_id}, type='{self.type}', amount={self.amount})>"


# Константы типов транзакций
class TransactionType:
    """Типы транзакций"""

    TOPUP = "TOPUP"  # Пополнение баланса
    CHARGE = "CHARGE"  # Списание при получении SMS
    DEDUCT = "DEDUCT"  # Списание администратором
    INITIAL_BALANCE = "INITIAL_BALANCE"  # Начальный баланс
