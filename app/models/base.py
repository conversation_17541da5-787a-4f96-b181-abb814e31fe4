"""
Базовые модели и импорты для всех моделей БД
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import Optional
import uuid
from uuid import uuid4

from sqlalchemy import (
    Boolean,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

from .activation import Activation
from .country import Country
from .log import Log
from .price import Price
from .service import Service
from .setting import Setting
from .transaction import Transaction

# Импорты всех моделей для удобства
from .user import User

__all__ = [
    "Base",
    "User",
    "Country",
    "Service",
    "Price",
    "Activation",
    "Transaction",
    "Log",
    "Setting",
]
