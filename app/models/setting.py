"""
Модель настроек системы
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import Boolean, DateTime, Integer, Numeric, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column

from app.core.database import Base


class Setting(Base):
    """Модель настроек системы"""

    __tablename__ = "settings"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)

    # SMS провайдеры - настройки перенесены в таблицу sms_providers
    # Используйте административную панель для управления провайдерами

    # Системные настройки
    debug_mode: Mapped[bool] = mapped_column(Boolean, default=False)
    log_level: Mapped[str] = mapped_column(
        String(20), default="INFO"
    )  # DEBUG, INFO, WARNING, ERROR, CRITICAL

    updated_at: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )

    def __repr__(self) -> str:
        return f"<Setting(id={self.id}, debug={self.debug_mode}, log_level={self.log_level})>"
