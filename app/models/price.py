"""
Модель цен
"""
from __future__ import annotations

from decimal import Decimal
from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON><PERSON>ey, Integer, Numeric
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

if TYPE_CHECKING:
    from .country import Country
    from .service import Service


class Price(Base):
    """Модель цен"""

    __tablename__ = "prices"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    country_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("countries.id"), nullable=False
    )
    service_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("services.id"), nullable=False
    )
    price: Mapped[Decimal] = mapped_column(Numeric(10, 3), nullable=False)
    provider_price_firefox: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3))
    provider_price_smslive: Mapped[Optional[Decimal]] = mapped_column(Numeric(10, 3))
    available: Mapped[Optional[int]] = mapped_column(Integer)

    # Связи
    country: Mapped["Country"] = relationship("Country", back_populates="prices")
    service: Mapped["Service"] = relationship("Service", back_populates="prices")

    def __repr__(self) -> str:
        return f"<Price(id={self.id}, price={self.price}, country_id={self.country_id}, service_id={self.service_id})>"
