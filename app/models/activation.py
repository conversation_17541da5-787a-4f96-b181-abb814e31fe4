"""
Модель активации номера
"""
from __future__ import annotations

from datetime import datetime
from decimal import Decimal
import random
from typing import TYPE_CHECKING, Optional

from sqlalchemy import (
    BigInteger,
    Boolean,
    DateTime,
    ForeignKey,
    Integer,
    Numeric,
    String,
    func,
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import Base

if TYPE_CHECKING:
    from .country import Country
    from .service import Service
    from .transaction import Transaction
    from .user import User


def generate_activation_id() -> int:
    """Генерирует уникальный 12-значный ID для активации"""
    # Генерируем случайное число от 100000000000 до 999999999999 (12 цифр)
    return random.randint(100000000000, 999999999999)


class Activation(Base):
    """Модель активации номера"""

    __tablename__ = "activations"

    id: Mapped[int] = mapped_column(
        BigInteger, primary_key=True, default=generate_activation_id
    )
    user_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("users.id"), nullable=False
    )
    provider: Mapped[str] = mapped_column(
        String(20), nullable=False
    )  # firefox, smslive
    provider_ref: Mapped[Optional[str]] = mapped_column(String(100))
    country_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("countries.id"), nullable=False
    )
    service_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("services.id"), nullable=False
    )
    phone_number: Mapped[Optional[str]] = mapped_column(String(20))
    status: Mapped[str] = mapped_column(
        String(20), default="WAIT_SMS"
    )  # WAIT_SMS, SMS_RECEIVED, CANCELED, TIMEOUT
    sms_code: Mapped[Optional[str]] = mapped_column(String(20))
    ordered_at: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    cost: Mapped[Decimal] = mapped_column(Numeric(10, 3), nullable=False)
    charged: Mapped[bool] = mapped_column(Boolean, default=False)

    # Связи
    user: Mapped["User"] = relationship("User", back_populates="activations")
    country: Mapped["Country"] = relationship("Country", back_populates="activations")
    service: Mapped["Service"] = relationship("Service", back_populates="activations")
    transactions: Mapped[list["Transaction"]] = relationship(
        "Transaction", back_populates="activation"
    )

    def __repr__(self) -> str:
        return f"<Activation(id={self.id}, user_id={self.user_id}, status='{self.status}', phone='{self.phone_number}')>"
