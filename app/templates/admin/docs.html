<!DOCTYPE html>
<html>
<head>
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui.css">
    <link rel="shortcut icon" href="https://fastapi.tiangolo.com/img/favicon.png">
    <title>SMS Proxy Service - Admin Panel</title>
    <style>
        .auth-form {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .auth-card {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 90%;
        }
        .auth-card h2 {
            margin-top: 0;
            text-align: center;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
            text-align: center;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Форма авторизации -->
    <div id="authForm" class="auth-form">
        <div class="auth-card">
            <h2>Админская документация</h2>
            <p style="text-align: center; color: #666; margin-bottom: 20px;">
                Введите API ключ администратора для доступа к документации
            </p>
            <form id="docsLoginForm">
                <div class="form-group">
                    <label for="docsApiKey">API ключ:</label>
                    <input type="password" id="docsApiKey" name="apiKey" required
                           placeholder="Введите ваш API ключ администратора">
                </div>
                <button type="submit" class="btn">Войти</button>
                <div id="docsError" class="error hidden"></div>
            </form>
        </div>
    </div>

    <!-- Swagger UI -->
    <div id="swagger-ui" class="hidden"></div>

    <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
    <script>
        // Переменные для документации (с префиксом docs чтобы не конфликтовать)
        let docsCurrentApiKey = '';

        console.log('Админская документация загружена');

        // Проверяем сохраненный API ключ
        const docsSavedApiKey = localStorage.getItem('admin_api_key');
        console.log('Сохраненный API ключ:', docsSavedApiKey ? 'найден' : 'не найден');

        if (docsSavedApiKey) {
            console.log('Проверяем сохраненный API ключ...');
            docsVerifyApiKey(docsSavedApiKey);
        } else {
            console.log('API ключ не найден, показываем форму авторизации');
            docsShowAuthForm();
        }

        // Обработка формы авторизации (переименовано в docsLoginForm чтобы не конфликтовать)
        document.getElementById('docsLoginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const apiKey = document.getElementById('docsApiKey').value;
            console.log('Попытка авторизации с введенным API ключом');
            await docsVerifyApiKey(apiKey);
        });

        // Показать форму авторизации (с префиксом docs)
        function docsShowAuthForm() {
            console.log('Показываем форму авторизации документации');
            document.getElementById('authForm').classList.remove('hidden');
            document.getElementById('swagger-ui').classList.add('hidden');
        }

        // Проверка API ключа (с префиксом docs)
        async function docsVerifyApiKey(apiKey) {
            console.log('Проверяем API ключ для документации...');
            try {
                const response = await fetch('/admin/verify-api-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey
                    },
                    body: JSON.stringify({})
                });

                console.log('Ответ сервера:', response.status);

                if (response.ok) {
                    console.log('API ключ валидный, показываем Swagger UI');
                    // API ключ валидный
                    docsCurrentApiKey = apiKey;
                    localStorage.setItem('admin_api_key', apiKey);
                    docsShowSwaggerUI();
                } else {
                    console.log('API ключ невалидный');
                    // Очищаем сохраненный ключ если он невалидный
                    localStorage.removeItem('admin_api_key');
                    docsShowAuthForm();
                    docsShowError('Неверный API ключ администратора');
                }
            } catch (error) {
                console.error('Ошибка при проверке API ключа:', error);
                localStorage.removeItem('admin_api_key');
                docsShowAuthForm();
                docsShowError('Ошибка проверки API ключа');
            }
        }

        // Показать Swagger UI (с префиксом docs)
        function docsShowSwaggerUI() {
            console.log('Показываем Swagger UI');
            document.getElementById('authForm').classList.add('hidden');
            document.getElementById('swagger-ui').classList.remove('hidden');

            const ui = SwaggerUIBundle({
                url: '/admin/openapi.json',
                dom_id: '#swagger-ui',
                layout: 'BaseLayout',
                deepLinking: true,
                showExtensions: true,
                showCommonExtensions: true,
                requestInterceptor: (request) => {
                    // Добавляем X-API-Key заголовок для всех запросов
                    request.headers['X-API-Key'] = docsCurrentApiKey;
                    return request;
                }
            });
        }

        // Показать ошибку (с префиксом docs)
        function docsShowError(message) {
            console.log('Показываем ошибку документации:', message);
            const errorDiv = document.getElementById('docsError');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');

            // Скрыть ошибку через 5 секунд
            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 5000);
        }
    </script>
</body>
</html>
